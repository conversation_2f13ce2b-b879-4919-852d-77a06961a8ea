package com.tal.sea.seaover.application.dto.sn;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InnerBindReqVo {
    @Schema(description = "设备sn号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "sn  cannot be empty")
    String sn;
    @Schema(description = "家长TalId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotEmpty(message = "parentTalId  cannot be empty")
    String parentTalId;
    @Schema(description = "孩子talId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotEmpty(message = "childTalId  cannot be empty")
    String childTalId;
}
