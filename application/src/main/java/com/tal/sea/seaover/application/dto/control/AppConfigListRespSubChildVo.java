package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AppConfigListRespSubChildVo {
    @Schema(description = "suiteKey", example = "xxx")
    private String suiteKey;
    @Schema(description = "suiteName", example = "xxx")
    private String suiteName;
    @Schema(description = "suiteDesc", example = "xxx")
    private String suiteDesc;
    @Schema(description = "管控集合", example = "xxx")
    private List<ControlRespChildUnitVo> unit_list;

}
