package com.tal.sea.seaover.application.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.*;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.dto.user.CubeLogoutVo;
import com.tal.sea.seaover.application.dto.user.FamilyLogOutRequestVo;
import com.tal.sea.seaover.application.dto.user.SnInfo;
import com.tal.sea.seaover.application.dto.user.SnInfoRequestVo;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.*;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.service.user.FamilyService;
import com.tal.sea.seaover.application.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.constant.RedisConstant.SN_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FamilyServiceImpl implements FamilyService {

    @Autowired
    private TalUserService talUserService;
    @Autowired
    private UserCenterFeign userCenterFeign;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TalParentServerProperties talParentServerProperties;
    @Autowired
    private TalSmsService talSmsService;
    @Autowired
    private AlarmXtqService alarmXtqService;
    @Autowired
    private DeviceFeign deviceFeign;
    @Autowired
    private TalCubeService talCubeService;

    @Override
    public FamilyInfo getFamilyInfo(String talId, String os) {
        FamilyInfo familyInfo = FamilyInfo.builder().build();
        //0、根据talId获取用户信息拿到familyIds
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(talId, os);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            return familyInfo;
        }
        if (StrUtil.isBlank(userInfoData.getData().getFamily_id())) {
            //当前还未组建家庭
            return familyInfo;
        }
        //1、根据familyIds 循环查家庭成员-登录过的设备列表  https://yapi.xesv5.com/project/4279/interface/api/132096
        String[] familyIdArray = userInfoData.getData().getFamily_id().split(",");
        Map<String, FamilyChildInfo> childMap = new HashMap<>();
        for (String familyId : familyIdArray) {
            //获取家庭成员-设备列表
            ServiceResult<TalFamilyUserAndSnInfo> familyUserSnInfo = talUserService.queryChildAndSnListByFamilyId(familyId, os);
            if (!familyUserSnInfo.isSuccess() || familyUserSnInfo.getData() == null || CollectionUtil.isEmpty(familyUserSnInfo.getData().getHome_list())) {
                continue;
            }
            familyUserSnInfo.getData().getHome_list().forEach(l -> {
                String deviceSn = null;
                boolean snState = StrUtil.isNotBlank(l.getDevice_sn()) && l.getLogin_status() != null && l.getLogin_status() == 1;
                if (snState) {
                    //二次校验设备是否游离
                    ServiceResult<TalDeviceSnBindInfo> talDeviceSnBindInfo = talUserService.queryDeviceBySn(l.getDevice_sn());
                    if (talDeviceSnBindInfo.isSuccess() && talDeviceSnBindInfo.getData() != null && StrUtil.isNotBlank(talDeviceSnBindInfo.getData().getFamily_id())) {
                        deviceSn = l.getDevice_sn();
                    }
                }
                FamilyChildInfo childInfo = childMap.get(l.getStu_talid());
                if (childInfo == null) {
                    childMap.put(l.getStu_talid(), FamilyChildInfo.builder()
                            .talId(l.getStu_talid())
                            .nickname(l.getNickname())
                            .avatarUrl(l.getAvatar_url())
                            .sn(deviceSn)
                            .build());
                } else {
                    if (StrUtil.isNotBlank(deviceSn)) {
                        //二次校验设备是否游离
                        childMap.put(l.getStu_talid(), FamilyChildInfo.builder()
                                .talId(l.getStu_talid())
                                .nickname(l.getNickname())
                                .avatarUrl(l.getAvatar_url())
                                .sn(deviceSn)
                                .build());
                    }
                }
            });
        }
        List<FamilyChildInfo> childInfos = childMap.values().stream().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(childInfos)) {
            return familyInfo;
        }
        //2、根据sn号获取设备sn对应的设备型号、类型、图片
        for (FamilyChildInfo childInfo : childInfos) {
            if (StrUtil.isBlank(childInfo.getSn())) {
                continue;
            }
            String redisVal = redisService.getString(SN_INFO_KEY + childInfo.getSn());
            if (StrUtil.isNotBlank(redisVal)) {
                SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
                childInfo.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
                childInfo.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
                childInfo.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
                continue;
            }
            SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(childInfo.getSn()).parentTalId(talId).build();
            JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(requestVo.getDeviceSn());
            log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", requestVo, snInfoJsonResult);
            if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
                SnInfo snInfo = snInfoJsonResult.getData();
                childInfo.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
                childInfo.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
                childInfo.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
                redisService.setString(SN_INFO_KEY + childInfo.getSn(), GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
            }
        }
        familyInfo.setChildInfoList(childInfos);
        return familyInfo;
    }

    @Override
    public String childLogout(String talId, String childTalId, String sn) {
        //1 查询设备类型
        Integer deviceType = getDeviceInfoBySn(sn, talId);

        if (deviceType == null || deviceType == 0) {
            //学习机流程
            return childLogoutByLearningMachine(talId, childTalId, sn);
        } else {
            //闹钟流程
            return childLogoutByAlarmMachine(childTalId,sn);
        }
    }

    private String childLogoutByAlarmMachine(String childTalId, String sn) {
        CubeLogoutVo reqVo = CubeLogoutVo.builder().childId(childTalId).build();
        JsonResult<Boolean> jsonResult = userCenterFeign.cubeLogout(reqVo);
        log.info("userCenterFeignInfo 【cubeLogout】 : reqVo: {}, result:{}", reqVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val() || jsonResult.getData() == null || !jsonResult.getData()) {
            alarmXtqService.alarmAndLogForError(String.format("家长端闹钟退出调用接口失败, reqVo: %s result: %s", reqVo, jsonResult), null);
            return null;
        }
        //成功之后调用设备退出接口
        talCubeService.logout(childTalId,sn);
        return null;
    }

    private String childLogoutByLearningMachine(String talId, String childTalId, String sn) {
        //1 发送push消息
        // 通知消息
        Boolean sendState = talSmsService.sendMessage(Collections.singletonList(childTalId), PushParam.SmsTypeEnum.NOTICE.getType(), TalParentServerProperties.SmsBizEnum.CHILD_LOGOUT_NOTICE.getName());
        if (!sendState) {
            return ResultEnum.USER_INNER_ERROR.msg();
        }
        // 静默消息
        Boolean sendState2 = talSmsService.sendMessage(Collections.singletonList(childTalId), PushParam.SmsTypeEnum.SILENT.getType(), TalParentServerProperties.SmsBizEnum.CHILD_LOGOUT.getName());
        if (!sendState2) {
            return ResultEnum.USER_INNER_ERROR.msg();
        }
        //2 同步616
        FamilyLogOutRequestVo requestVo = FamilyLogOutRequestVo.builder().talId(childTalId).deviceSn(sn).build();
        JsonResult jsonResult = userCenterFeign.childLogout(requestVo);
        log.info("userCenterFeignInfo 【childLogout】 : reqVo: {}, result:{}", requestVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            log.error("家长端将孩子退出登录上报失败, parentTalId: {}, childTalId: {}, result: {}", talId, childTalId, jsonResult);
            alarmXtqService.alarm(String.format("家长端将孩子退出登录上报失败, parentTalId: %s, childTalId: %s, result: %s", talId, childTalId, jsonResult));
            return (jsonResult != null && StrUtil.isNotBlank(jsonResult.getMessage())) ? jsonResult.getMessage() : ResultEnum.USER_INNER_ERROR.msg();
        }
        return null;
    }

    private Integer getDeviceInfoBySn(String sn, String talId) {
        String redisVal = redisService.getString(SN_INFO_KEY + sn);
        if (StrUtil.isNotBlank(redisVal)) {
            SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
            return snInfo.getDeviceType();
        }
        SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(sn).parentTalId(talId).build();
        JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(requestVo.getDeviceSn());
        log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", requestVo, snInfoJsonResult);
        if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
            SnInfo snInfo = snInfoJsonResult.getData();
            redisService.setString(SN_INFO_KEY + sn, GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
            return snInfo.getDeviceType();
        }
        return null;
    }

    @Override
    public String queryParentTalIdByChildTalId(String childTalId) {
        ServiceResult<TalUserInfoPlus> talUserInfoPlusServiceResult = talUserService.queryChildUserInfoPlus(childTalId);
        TalUserInfoPlus talUserInfoPlus = talUserInfoPlusServiceResult.getData();
        if (talUserInfoPlus == null) {
            return null;
        }
        String familyId = talUserInfoPlus.getFamily_id();
        if (StrUtil.isBlank(familyId)) {
            return null;
        }
        ServiceResult<FamilyMemberInfo> familyMemberInfoServiceResult = talUserService.queryFamilyMemberById(familyId);
        FamilyMemberInfo familyMemberInfo = familyMemberInfoServiceResult.getData();
        if (familyMemberInfo == null || CollectionUtil.isEmpty(familyMemberInfo.getList())) {
            return null;
        }
        return familyMemberInfo.getList().stream()
                .filter(item -> item.getFamily_role() != RoleTypeEnum.STUDENT.val() && item.getIs_manager() == RoleTypeEnum.ADMIN.val())
                .map(FamilyMemberChildInfo::getTal_id)
                .findFirst()
                .orElse("");
    }

    @Override
    public boolean sendPushQueryDeviceList(String parentTalId) {
        return talSmsService.sendMessage(Collections.singletonList(parentTalId), PushParam.SmsTypeEnum.SILENT.getType(), TalParentServerProperties.SmsBizEnum.PARENT_QUERY_DEVICE_LIST.getName());
    }

    @Override
    public FamilyChildren getFamilyChildList(String talId, String os) {
        FamilyChildren familyInfo = FamilyChildren.builder().parentTalId(talId).build();
        //0、根据talId获取用户信息拿到familyIds
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(talId, os);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            return familyInfo;
        }
        if (StrUtil.isBlank(userInfoData.getData().getFamily_id())) {
            //当前还未组建家庭
            return familyInfo;
        }
        //1、根据familyIds 循环查家庭成员-筛选孩子信息
        String[] familyIdArray = userInfoData.getData().getFamily_id().split(",");
        List<FamilyChildUserInfo> childList = new ArrayList<>();
        for (String familyId : familyIdArray) {
            //获取家庭成员列表
            ServiceResult<FamilyMemberInfo> familyMember = talUserService.queryFamilyMemberById(familyId);
            if (!familyMember.isSuccess() || familyMember.getData() == null || CollectionUtil.isEmpty(familyMember.getData().getList())) {
                continue;
            }
            familyMember.getData().getList().forEach(l -> {
                if (l.getFamily_role() != null && l.getFamily_role() == RoleTypeEnum.STUDENT.val()) {
                    childList.add(FamilyChildUserInfo.builder().talId(l.getTal_id()).nickname(l.getNickname()).avatarUrl(l.getAvatar_url()).grade(StrUtil.isNotBlank(l.getGrade()) ? Long.parseLong(l.getGrade()) : null).sex(StrUtil.isNotBlank(l.getSex()) ? Integer.parseInt(l.getSex()) : null).build());
                }
            });
        }
        childList.sort(Comparator.comparing(FamilyChildUserInfo::getTalId));
        familyInfo.setChildList(childList);
        return familyInfo;
    }

    @Override
    public ChildAddAndBindRespVo addChild(String parentTalId, String os, ChildAddAndBindReqVo reqVo) {
        ChildAddAndBindRespVo respVo = ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.SUCCESS).build();
        //1 新建用户
        ServiceResult<TalAddUserResult> addResult = talUserService.createAccount(reqVo.getNickname(), reqVo.getGrade(), reqVo.getSex());
        if (!addResult.isSuccess() || addResult.getData() == null || StrUtil.isBlank(addResult.getData().getTal_id())) {
            return ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.ADD_CHILD_ERROR_1).build();
        }
        respVo.setTalId(addResult.getData().getTal_id());
        //2 维护孩子头像
        ServiceResult<TalUserInfoPlus> modifyResult = talUserService.modifyUserInfo(respVo.getTalId(), null, reqVo.getAvatarUrl(),  null, os);
        if (!modifyResult.isSuccess() || modifyResult.getData() == null) {
            return ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.ADD_CHILD_ERROR_1).build();
        }
        //3 获取家长是否已有家庭关系
        ServiceResult<TalUserInfo> parentUserInfo = talUserService.queryUserInfoWithOutOs(parentTalId);
        if (!parentUserInfo.isSuccess() || parentUserInfo.getData() == null) {
            return ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.ADD_CHILD_ERROR_2).build();
        }
        //4 创建家庭
        String familyId = null;
        if (StrUtil.isBlank(parentUserInfo.getData().getFamily_id())) {
            ServiceResult<TalCreateFamilyAndBindSnInfo> createResult = talUserService.createFamilyAndBindSn(parentTalId, respVo.getTalId(), null);
            if (!createResult.isSuccess() || createResult.getData() == null) {
                return ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.ADD_CHILD_ERROR_3).build();
            }
            familyId = createResult.getData().getFamily_id();
        } else {
            //5 加入家庭
            familyId = parentUserInfo.getData().getFamily_id();
            ServiceResult<TalJoinFamilyAndBindSnInfo> joinResult = talUserService.joinFamilyAndBindSn(respVo.getTalId(), null, familyId);
            if (!joinResult.isSuccess() || joinResult.getData() == null) {
                return ChildAddAndBindRespVo.builder().resultEnum(ResultEnum.ADD_CHILD_ERROR_4).build();
            }
        }
        respVo.setFamilyId(familyId);
        return respVo;
    }

    @Override
    public Boolean modifyChild(String parentTalId, String os, ChildModifyReqVo reqVo) {
        ServiceResult<TalUserInfoPlus> modifyResult = talUserService.modifyUserInfo(reqVo.getTalId(), reqVo.getNickname(), reqVo.getAvatarUrl(), reqVo.getGrade(), os);
        return (!modifyResult.isSuccess() || modifyResult.getData() == null) ? false : true;
    }

    @Override
    public FamilyInfoV2 getFamilyInfoV2(String talId, String os) {
        FamilyInfoV2 familyInfo = FamilyInfoV2.builder().build();
        //0、根据talId获取用户信息拿到familyIds
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(talId, os);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            return familyInfo;
        }
        if (StrUtil.isBlank(userInfoData.getData().getFamily_id())) {
            //当前还未组建家庭
            return familyInfo;
        }
        //1、根据familyIds 循环查家庭成员-登录过的设备列表  https://yapi.xesv5.com/project/4279/interface/api/132096
        String[] familyIdArray = userInfoData.getData().getFamily_id().split(",");
        Map<String, FamilyChildInfoV2> childMap = new HashMap<>();
        for (String familyId : familyIdArray) {
            //获取家庭成员-设备列表
            ServiceResult<TalFamilyUserAndSnInfo> familyUserSnInfo = talUserService.queryChildAndSnListByFamilyId(familyId, os);
            if (!familyUserSnInfo.isSuccess() || familyUserSnInfo.getData() == null || CollectionUtil.isEmpty(familyUserSnInfo.getData().getHome_list())) {
                continue;
            }
            familyUserSnInfo.getData().getHome_list().forEach(l -> {
                String deviceSn = null;
                //二次校验设备是否游离
                boolean snState = StrUtil.isNotBlank(l.getDevice_sn()) && l.getLogin_status() != null && l.getLogin_status() == 1;
                if (snState) {
                    ServiceResult<TalDeviceSnBindInfo> talDeviceSnBindInfo = talUserService.queryDeviceBySn(l.getDevice_sn());
                    if (talDeviceSnBindInfo.isSuccess() && talDeviceSnBindInfo.getData() != null && StrUtil.isNotBlank(talDeviceSnBindInfo.getData().getFamily_id()) && familyId.equals(talDeviceSnBindInfo.getData().getFamily_id())) {
                        deviceSn = l.getDevice_sn();
                    }
                }
                FamilyChildInfoV2 childInfo = childMap.get(l.getStu_talid());
                if (childInfo == null) {
                    List<FamilyChildInfoBody> snList = new ArrayList<>();
                    if (StrUtil.isNotBlank(deviceSn)) {
                        snList.add(FamilyChildInfoBody.builder().sn(deviceSn).build());
                    }
                    childMap.put(l.getStu_talid(), FamilyChildInfoV2.builder()
                            .talId(l.getStu_talid())
                            .nickname(l.getNickname())
                            .avatarUrl(l.getAvatar_url())
                            .snList(snList)
                            .build());
                } else {
                    if (StrUtil.isNotBlank(deviceSn)) {
                        childInfo.getSnList().add(FamilyChildInfoBody.builder().sn(deviceSn).build());
                        childMap.put(l.getStu_talid(), childInfo);
                    }
                }
            });
        }
        List<FamilyChildInfoV2> childInfos = childMap.values().stream().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(childInfos)) {
            return familyInfo;
        }
        //2、根据sn号获取设备sn对应的设备型号、类型、图片
        for (FamilyChildInfoV2 childInfo : childInfos) {
            if (CollectionUtil.isEmpty(childInfo.getSnList())) {
                continue;
            }
            for (FamilyChildInfoBody device : childInfo.getSnList()) {
                initDeviceInfo(device, talId);
            }
        }
        //将设备列表去重，避免设备端同一用户多设备登录的异常情况
        childInfos = deduplicateByDeviceTypeAndSnModel(childInfos);
        familyInfo.setChildInfoList(childInfos);
        return familyInfo;
    }

    private List<FamilyChildInfoV2> deduplicateByDeviceTypeAndSnModel(List<FamilyChildInfoV2> childInfos) {
        List<FamilyChildInfoV2> result = new ArrayList<>();
        for (int i = 0; i < childInfos.size(); i++) {
            FamilyChildInfoV2 childInfoV2 = childInfos.get(i);
            Map<String, FamilyChildInfoBody> map = new LinkedHashMap<>();
            for (FamilyChildInfoBody item : childInfoV2.getSnList()) {
                String key = item.getDeviceType() + "-" + item.getSnModel();
                map.put(key, item);
            }
            childInfoV2.setSnList(new ArrayList<>(map.values()));
            result.add(childInfoV2);
        }
        return result;
    }

    private void initDeviceInfo(FamilyChildInfoBody device, String talId) {
        String redisVal = redisService.getString(SN_INFO_KEY + device.getSn());
        if (StrUtil.isNotBlank(redisVal)) {
            SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
            device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
            device.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
            return;
        }
        SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(device.getSn()).parentTalId(talId).build();
        JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(requestVo.getDeviceSn());
        log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", requestVo, snInfoJsonResult);
        if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
            SnInfo snInfo = snInfoJsonResult.getData();
            device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
            device.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
            redisService.setString(SN_INFO_KEY + device.getSn(), GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
        }
    }
}
