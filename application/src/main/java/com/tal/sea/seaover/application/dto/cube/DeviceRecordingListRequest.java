package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备端查询留言列表请求
 */
@Data
public class DeviceRecordingListRequest implements Serializable {

    /**
     * 用户ID
     */
    private String talId;

    /**
     * 设备sn
     */
    private String sn;

    /**
     * 最后ID值
     */
    private Long lastId;

    /**
     * 页大小
     */
    @NotNull(message = "pageSize cannot be null")
    @Min(value = 1, message = "pageSize must be greater than 0")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "page cannot be null")
    @Min(value = 1, message = "page must be greater than 0")
    private Integer page;
}
