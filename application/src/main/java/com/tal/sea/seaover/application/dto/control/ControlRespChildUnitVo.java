package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ControlRespChildUnitVo {
    @Schema(description = "管控单元key", example = "xxx")
    private String unitKey;
    @Schema(description = "管控单元值", example = "xxx")
    private String unitValue;
    @Schema(description = "管控单元名称", example = "xxx")
    private String unitName;
    @Schema(description = "管控单元描述", example = "xxx")
    private String unitDesc;
    @Schema(description = "管控单元id", example = "xxx")
    private Long id;
    @Schema(description = "管控单元 pid", example = "xxx")
    private Long pid;
    @Schema(description = "管控单元类型", example = "xxx")
    private Integer type;

    @Schema(description = "子管控单元", example = "xxx")
    private List<ControlRespChildUnitVo> child;
}
