package com.tal.sea.seaover.application.dto.cube;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户日程信息
 */
@Data
public class UserSchedule{
    /**
     * 主键ID
     */
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一UUID
     */
    private String unionId;

    /**
     * 日程名称
     */
    private String name;

    /**
     * 日程时间 24小时制的秒数
     */
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    private Integer notifyDuration;

    /**
     * 重复日 1-7
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * icon url
     */
    private String iconUrl;

    /**
     * label颜色
     */
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 是否预置日程 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 同步状态，0-未同步，1-已同步
     */
    private Integer syncStatus;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}