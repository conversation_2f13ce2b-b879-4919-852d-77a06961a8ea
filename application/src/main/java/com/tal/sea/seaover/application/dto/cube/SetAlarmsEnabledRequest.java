package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetAlarmsEnabledRequest{
    /**
     * alarmId
     */
    @NotNull(message = "alarmId  cannot be empty")
    private Long alarmId;
    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "闹钟是否重复  cannot be empty")
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;
    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    /**
     * 是否启用
     */
    @NotBlank(message = "enabled  cannot be empty")
    private Integer enabled;
    /**
     * 最后修改人
     */
    @NotBlank(message = "lastModifiedBy  cannot be empty")
    private Integer lastModifiedBy;
}
