package com.tal.sea.seaover.application.config;

import com.tal.sea.seaover.application.dto.common.ParentDeviceData;
import com.tal.sea.seaover.application.dto.family.FamilySnRoleChildInfo;
import com.tal.sea.seaover.application.dto.sms.ChildLogOutSmsInfoConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author:zgy
 */
@Data
@Component
@ConfigurationProperties(prefix = "parent")
public class TalParentServerProperties {

    private List<FamilySnRoleChildInfo> familyRoles;
    /**
     * 家长端默认头像
     */
    private String defaultAvatar;
    /**
     * 消息中心对应配置
     */
    private Map<String, SmsPushData> pushMessages;
    /**
     * 学习机绑定超时时间
     */
    private Integer bindWaitingTime;
    /**
     * 设备列表配置
     */
    private List<ParentDeviceData> deviceList;
    /**
     * 家长端cube相关用户头像选择列表-男性
     */
    private List<String> boyAvatarList;
    /**
     * 家长端cube相关用户头像选择列表-女性
     */
    private List<String> girlAvatarList;

    /**
     * 删除账号展示文案-前部分配置
     */
    private List<String> accountDeleteBeforeItem;
    /**
     * 删除账号展示文案-闹钟个性化配置
     */
    private String accountDeleteClockItem;
    /**
     * push消息最多重试次数
     */
    private Integer pushMsgRetryMaxCount;
    /**
     * 拍搜最小支持的OS版本
     */
    private String paiSouMinOsVersion;

    /**
     * 拍搜管控集合key
     */
    private String paiSouControlSuiteKey;

    @Data
    public static class SmsPushData {
        private String appId;
        private String title;
        private String body;
        private String topic;
        private String data;
    }

    // 业务类型
    public enum SmsBizEnum {
        // 孩子登出
        CHILD_LOGOUT("1", "childLogOut"),
        //家长管控推送
        PARENT_USER_CONTROL_PUSH("2", "parentUserControl"),
        PARENT_SN_CONTROL_PUSH("3", "parentSnControl"),
        //重新发起设备列表查询
        PARENT_QUERY_DEVICE_LIST("4", "parentDeviceList"),
        //孩子设备注销通知消息
        CHILD_DEVICE_DELETE("5", "childDeviceDelete"),
        //孩子退出登录通知消息
        CHILD_LOGOUT_NOTICE("6", "childLogoutNotice");
        String type;
        String name;

        SmsBizEnum(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public String getName() {
            return name;
        }
    }
}
