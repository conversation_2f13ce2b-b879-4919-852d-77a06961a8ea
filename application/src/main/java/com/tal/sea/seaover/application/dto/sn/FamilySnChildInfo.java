package com.tal.sea.seaover.application.dto.sn;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilySnChildInfo {

    /**
     * 孩子用户ID
     */
    @Schema(description = "孩子用户ID", example = "user123")
    private String talId;
    /**
     * 孩子用户昵称
     */
    @Schema(description = "孩子用户昵称", example = "user123")
    private String nickName;

    /**
     * 登录的设备sn号
     */
    @Schema(description = "登录的设备sn号", example = "12345667")
    private String sn;
    /**
     * 登录的设备名称
     */
    @Schema(description = "登录的设备名称", example = "Thinkpal Tablet T100")
    private String snName;
    /**
     * 设备型号
     */
    @Schema(description = "登录的设备型号", example = "T100")
    private String snModel;
    /**
     * 设备图片
     */
    @Schema(description = "登录的设备sn图片", example = "http://111111.jpg")
    private String snImg;
    /**
     * 在线状态
     */
    @Schema(description = "在线状态", example = "true:在线 false:离线")
    private Boolean onlineState;
    /**
     * 设备类型 0 学习机 1 cube闹钟
     */
    @Schema(description = "设备类型 0 学习机 1 cube闹钟")
    private Integer deviceType;
    //登录状态
    private Boolean loginState;
}