package com.tal.sea.seaover.application.service.user;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.sn.CodeCheckBindReqVo;
import com.tal.sea.seaover.application.dto.sn.FamilySnInfo;
import com.tal.sea.seaover.application.dto.sn.InnerBindReqVo;
import com.tal.sea.seaover.application.dto.sn.SnCodeCheckAndBindResult;

/**
 * <AUTHOR>
 */
public interface SnService {
    /**
     * 获取家庭设备信息
     *
     * @param talId
     * @param os
     * @return
     */
    FamilySnInfo queryFamilySnInfo(String talId, String os);

    /**
     * 校验sn号
     *
     * @param talId
     * @param sn
     * @return
     */
    ResultEnum snCheck(String talId, String sn, String os);

    /**
     * 确认码校验+设备绑定
     *
     * @param reqVo
     * @return
     */
    SnCodeCheckAndBindResult codeCheckAndBind(CodeCheckBindReqVo reqVo);

    /**
     * 更新家庭角色Id
     *
     * @param talId  家长talId
     * @param roleId 角色Id
     * @param os
     * @return
     */
    ResultEnum updateFamilyRoleInfo(String talId, Integer roleId, String os);

    SnCodeCheckAndBindResult innerBind(InnerBindReqVo reqVo);

    SnCodeCheckAndBindResult codeCheckAndBindV2(CodeCheckBindReqVo reqVo);
}
