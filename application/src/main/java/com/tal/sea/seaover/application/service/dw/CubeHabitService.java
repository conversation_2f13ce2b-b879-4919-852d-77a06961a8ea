package com.tal.sea.seaover.application.service.dw;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.domain.CubeHabit;
import com.tal.sea.seaover.application.dto.cube.UserAlarm;
import com.tal.sea.seaover.application.dto.cube.UserSchedule;
import com.tal.sea.seaover.application.dto.studyinfo.CubeHabitInfoVo;
import com.tal.sea.seaover.application.dto.studyinfo.GetAlarmByUuidsReq;
import com.tal.sea.seaover.application.dto.studyinfo.GetScheduleByUuidsReq;
import com.tal.sea.seaover.application.enums.CubeHabitType;
import com.tal.sea.seaover.application.mapper.CubeHabitMapper;
import com.tal.sea.seaover.application.service.feign.CubeControlServerFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.tal.sea.seaover.application.enums.CubeHabitType.ALARM;

@Service
@Slf4j
public class CubeHabitService {

    @Autowired
    CubeHabitMapper cubeHabitMapper;

    @Autowired
    CubeControlServerFeign cubeControlServerFeign;

    public List<CubeHabitInfoVo> getCubeHabitInfo(String talId, String sn) {

        // 构建查询条件
        LambdaQueryWrapper<CubeHabit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CubeHabit::getSn, sn)
                .eq(CubeHabit::getTalId, talId)
                .orderByDesc(CubeHabit::getTotalFinishDay)
                .last("limit 50");
        List<CubeHabit> cubeHabits = cubeHabitMapper.selectList(queryWrapper);

        if (cubeHabits.isEmpty()) {
            return Collections.emptyList();
        }

        List<CubeHabitInfoVo> res = new ArrayList<>(50);
        // 记录id和排序序号映射
        Map<String, Integer> sortMap = new HashMap<>(50);
        // 闹钟和日程的uuid列表  查询使用
        List<String> alarmUuids = new ArrayList<>();
        List<String> scheduleUuids = new ArrayList<>();

        for (int i = 0; i < cubeHabits.size(); i++) {
            CubeHabit cubeHabit = cubeHabits.get(i);
            // 添加到结果列表
            res.add(getHabitVoFromDo(cubeHabit));
            // 记录排序序号
            sortMap.put(cubeHabit.getObjectId(), i);
            // 添加到uuid列表
            if (cubeHabit.getType().equals(ALARM.getType())) {
                alarmUuids.add(cubeHabit.getObjectId());
            } else if (cubeHabit.getType().equals(CubeHabitType.SCHEDULE.getType())) {
                scheduleUuids.add(cubeHabit.getObjectId());
            }
        }
        // 查询闹钟详情
        if (!alarmUuids.isEmpty()) {
            GetAlarmByUuidsReq alarmReq = new GetAlarmByUuidsReq();
            alarmReq.setUuids(alarmUuids);
            JsonResult<List<UserAlarm>> alarmResult = cubeControlServerFeign.alarmBatchListByUuIds(alarmReq);
            if (!alarmResult.isOk() || alarmResult.getData() == null) {
                throw new RuntimeException("get alarm detail failed");
            }

            // 填充闹钟name和iconUrl
            alarmResult.getData().forEach(alarmDetail -> {
                res.get(sortMap.get(alarmDetail.getUnionId())).getDetail().setName(alarmDetail.getAlarmName());
                res.get(sortMap.get(alarmDetail.getUnionId())).getDetail().setIconUrl(alarmDetail.getIconUrl());
            });
        }

//        查询计划详情
        if (!scheduleUuids.isEmpty()) {
            GetScheduleByUuidsReq scheduleReq = new GetScheduleByUuidsReq();
            scheduleReq.setUuids(scheduleUuids);
            JsonResult<List<UserSchedule>> scheduleResult = cubeControlServerFeign.scheduleBatchListByUuIds(scheduleReq);
            if (!scheduleResult.isOk() || scheduleResult.getData() == null) {
                throw new RuntimeException("get schedule detail failed");
            }

            // 填充日程name和iconUrl
            scheduleResult.getData().forEach(scheduleDetail -> {
                res.get(sortMap.get(scheduleDetail.getUnionId())).getDetail().setName(scheduleDetail.getName());
                res.get(sortMap.get(scheduleDetail.getUnionId())).getDetail().setIconUrl(scheduleDetail.getIconUrl());
            });
        }

        return res;

    }

    private CubeHabitInfoVo getHabitVoFromDo(CubeHabit cubeHabit) {
        CubeHabitInfoVo cubeHabitInfoVo = new CubeHabitInfoVo();
        cubeHabitInfoVo.setType(cubeHabit.getType());
        CubeHabitInfoVo.detail detailInfo = new CubeHabitInfoVo.detail();
        detailInfo.setId(cubeHabit.getObjectId());
        detailInfo.setWeekFinishDays(cubeHabit.getWeekFinishDay());
        detailInfo.setTotalFinishDays(cubeHabit.getTotalFinishDay());
        cubeHabitInfoVo.setDetail(detailInfo);
        return cubeHabitInfoVo;
    }


}
