package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyInfoV2 {
    /**
     * 家长TalID
     */
    @Schema(description = "家长TalID", example = "user123")
    private String parentTalId;
    /**
     * 孩子信息列表
     */
    @Schema(description = "孩子信息列表", example = "")
    private List<FamilyChildInfoV2> childInfoList;
}