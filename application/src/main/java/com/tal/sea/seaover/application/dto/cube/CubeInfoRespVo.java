package com.tal.sea.seaover.application.dto.cube;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CubeInfoRespVo {

    /**
     * 登录的设备sn号
     */
    @Schema(description = "登录的设备sn号")
    private String sn;
    /**
     * 登录的设备名称
     */
    @Schema(description = "登录的设备名称")
    private String snName;
    /**
     * 登录的设备型号
     */
    @Schema(description = "登录的设备型号")
    private String snModel;
    /**
     * 登录的设备sn图片
     */
    @Schema(description = "登录的设备sn图片")
    private String snImg;
    /**
     * 在线状态
     */
    @Schema(description = "在线状态")
    private Boolean onlineState;
    /**
     * 联网状态 true 联网中 false  未联网
     */
    //@Schema(description = "联网状态")
    //private Boolean networkState;
    /**
     * 电量
     */
    @Schema(description = "电量")
    private Integer battery;

    /**
     * 是否充电 1 充电中 0 未充电
     */
    @Schema(description = "是否充电")
    private Integer chargeState;

    /**
     * 当前已开启的闹钟数量
     */
    private Integer alarmCount;
    /**
     * 当前已开启的日程数量
     */
    private Integer scheduleCount;
}
