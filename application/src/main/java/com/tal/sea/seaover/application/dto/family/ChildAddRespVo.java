package com.tal.sea.seaover.application.dto.family;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChildAddRespVo {
    /**
     * 孩子talId
     */
    private String talId;
    /**
     * 家庭Id
     */
    private String familyId;
}
