package com.tal.sea.seaover.application.config.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MobileValidator implements ConstraintValidator<Mobile, String> {
    String mobileRegex = "^1[3-9]\\d{9}$";

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if (null == s || s == "") {
            return true;
        }
        if (s.matches(mobileRegex)) {
            return true;
        }
        return false;
    }
}
