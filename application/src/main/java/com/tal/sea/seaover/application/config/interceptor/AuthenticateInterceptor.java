package com.tal.sea.seaover.application.config.interceptor;


import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.annotation.ExcludeLoginVerify;
import com.tal.sea.seaover.application.config.exception.UnauthenticatedException;
import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.service.common.RedisService;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.util.GsonUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import static com.tal.sea.seaover.application.constant.RedisConstant.USER_LOGIN_SECRET_KEY;


/**
 * 登录拦截器
 */
//@Slf4j
//@Component
//public class AuthenticateInterceptor implements HandlerInterceptor {
public class AuthenticateInterceptor {


//    @Autowired
//    UserCenterFeign userCenterFeign;
//
//    @Autowired
//    RedisService redisService;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        if (handler instanceof HandlerMethod) {
//            final HandlerMethod handlerMethod = (HandlerMethod) handler;
//            final Class<?> clazz = handlerMethod.getBeanType();
//            final Method method = handlerMethod.getMethod();
//
//            if (clazz.isAnnotationPresent(ExcludeLoginVerify.class) || method.isAnnotationPresent(ExcludeLoginVerify.class)) {
//                return true;
//            }
//            if (!loginContextVerification(request)) {
//                throw new UnauthenticatedException();
//            }
//            return true;
//        }
//        return true;
//    }
//
//    /**
//     * 1 请求头校验
//     * 2 token请求616校验
//     * 3 sign签名校验
//     * <p>
//     * X-Tal-Sign 计算过程定义如下
//     * lower ( md5( ${X-Tal-Timestamp} + ${X-Tal-Nonce} + ${X-Tal-Id}  + secret) )
//     *
//     * @param request
//     */
//    private boolean loginContextVerification(HttpServletRequest request) {
//
//        String path = request.getRequestURI();
//        String authorization = request.getHeader(AppConstant.AUTHORIZATION);
//        String timestamp = request.getHeader(AppConstant.X_TAL_TIMESTAMP);
//        String nonce = request.getHeader(AppConstant.X_TAL_NONCE);
//        String sign = request.getHeader(AppConstant.X_TAL_SIGN);
//        String appVersion = request.getHeader(AppConstant.X_TAL_APP_VERSION);
//        String appOs = request.getHeader(AppConstant.X_TAL_OS);
//        String appGps = request.getHeader(AppConstant.X_TAL_GPS);
//        String appTimeZone = request.getHeader(AppConstant.X_TAL_TIME_ZONE);
//        String traceId = request.getHeader(AppConstant.X_TAL_TRACEID);
//        Map<String, String> headerMap = new HashMap<>();
//        headerMap.put(AppConstant.X_TAL_TIMESTAMP, timestamp);
//        headerMap.put(AppConstant.X_TAL_NONCE, nonce);
//        headerMap.put(AppConstant.X_TAL_SIGN, sign);
//        headerMap.put(AppConstant.X_TAL_APP_VERSION, appVersion);
//        //1   请求头判空校验
//        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
//            String key = entry.getKey();
//            String value = entry.getValue();
//            if (StringUtils.isBlank(value)) {
//                log.error("parent request auth error traceId:{} msg: header【{}】 is null | path:{} | header:{}", traceId, key, path, getRequestHeaderJsonStr(request));
//                return false;
//            }
//        }
//        //2   code登陆单独鉴权
//        if (path.startsWith("/api/user/codeLogin") && !checkSign(sign, timestamp, nonce, AppConstant.LOGIN_SECRET)) {
//            log.error("parent request auth error traceId:{} msg: header sign error | path:{} | header:{}", traceId, path, getRequestHeaderJsonStr(request));
//            return false;
//        }
//        //3  登录验证
//        String talId = "";
//        if (StrUtil.isBlank(authorization)) {
//            log.error("parent request auth error traceId:{} msg: header authorization is null | path:{} | header:{}", traceId, path, getRequestHeaderJsonStr(request));
//            return false;
//        }
//        talId = userCenterFeign.parentTokenCheck(authorization);
//        talId = "12345";
//        if (StrUtil.isBlank(talId)) {
//            log.error("parent request auth error traceId:{} msg: header token check error | path:{} | header:{}", traceId, path, getRequestHeaderJsonStr(request));
//            return false;
//        } else {
//            request.setAttribute("talId", talId);
//        }
//
//
//        //4  sign鉴权
//        if (StrUtil.isBlank(sign)) {
//            log.error("parent request auth error traceId:{} msg: header sign is null | path:{} | header:{}", traceId, path, getRequestHeaderJsonStr(request));
//            return false;
//        }
//        String secret = redisService.getString(USER_LOGIN_SECRET_KEY + talId);
//        if (StrUtil.isBlank(secret)) {
//            log.error("parent request auth error traceId:{} msg: secret is null talId:{} | path:{} | header:{}", traceId, talId, path, getRequestHeaderJsonStr(request));
//            return false;
//        }
//        if (!checkSign(sign, timestamp, nonce, secret)) {
//            log.error("parent request auth error traceId:{} msg: header sign2 error | path:{} | header:{}", traceId, path, getRequestHeaderJsonStr(request));
//            return false;
//        }
//        return true;
//    }
//
//    private String getRequestHeaderJsonStr(HttpServletRequest request) {
//        Map<String, String> headers = new HashMap<>();
//        // 获取所有请求头名称
//        Enumeration<String> headerNames = request.getHeaderNames();
//        while (headerNames.hasMoreElements()) {
//            String headerName = headerNames.nextElement();
//            String headerValue = request.getHeader(headerName);
//            headers.put(headerName, headerValue);
//        }
//        return GsonUtil.toJson(headers);
//    }
//
//    //sign校验
//    private boolean checkSign(String sign, String timestamp, String nonce, String secret) {
//        String signTarget = DigestUtils.md5DigestAsHex((timestamp + nonce + secret).getBytes()).toLowerCase();
//        log.info("x-tal-sign: {},  signTarget: {}", sign, signTarget);
//        return sign.equals(signTarget);
//    }
}
