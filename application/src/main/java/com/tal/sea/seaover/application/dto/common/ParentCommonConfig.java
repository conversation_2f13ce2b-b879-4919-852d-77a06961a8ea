package com.tal.sea.seaover.application.dto.common;

import lombok.Data;

import java.util.List;

@Data
public class ParentCommonConfig {
    /**
     * 家长端cube相关用户头像选择列表-男性
     */
    private List<String> boyAvatarList;
    /**
     * 家长端cube相关用户头像选择列表-女性
     */
    private List<String> girlAvatarList;

    /**
     * 闹钟icon配置列表
     */
    private List<CubeCommonConfig> alarmIconList;
    /**
     * 闹钟铃声配置列表
     */
    private List<CubeCommonConfig> alarmSoundList;
    /**
     * 日程配置icon列表
     */
    private List<CubeCommonConfig> scheduleIconList;
}
