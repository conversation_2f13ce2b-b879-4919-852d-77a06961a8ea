package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.cube.*;
import com.tal.sea.seaover.application.dto.studyinfo.GetAlarmByUuidsReq;
import com.tal.sea.seaover.application.dto.studyinfo.GetScheduleByUuidsReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

/**
 * Cube服务端
 *
 * <AUTHOR>
 */
@FeignClient(name = "cube-703-datahub-application", path = "/inner/datahub/cube")
public interface CubeControlServerFeign {

    /**
     * 查询已开启的闹钟数量
     *
     * @return
     */
    @PostMapping("/alarm/getOpenCount")
    JsonResult<Integer> queryCubeAlarmCount(CubeAlarmCountReqVo reqVo);

    /**
     * 查询已开启的日程数量
     *
     * @return
     */
    @PostMapping("/schedule/getOpenCount")
    JsonResult<Integer> queryCubeScheduleCount(CubeScheduleCountReqVo scheduleCountReqVo);

    /**
     * 获取闹钟icon/铃声列表
     * 获取闹钟日程icon列表
     *
     * @return
     */
    @GetMapping("/resources/listAlarmsIconAndSound")
    JsonResult<Map<Integer, List<CubeResources>>> listIconAndSound();

    /**
     * 获取闹钟icon/铃声列表
     * 获取闹钟日程icon列表
     *
     * @return
     */
    @GetMapping("/resources/listScheduleIcon")
    JsonResult<Map<Integer, List<CubeResources>>> listScheduleIcon();

    /**
     * 获取闹钟列表
     *
     * @return
     */
    @PostMapping("/alarm/list")
    JsonResult<List<CubeAlarmRespVo>> queryCubeAlarmList(CubeReqVo cubeReqVo);
    
    /**
     * 新增闹钟
     *
     * @return
     */
    @PostMapping("/alarm/add")
    JsonResult<AlarmsDetailResponse> addAlarm(AlarmsAddRequest cubeReqVo);

    /**
     * 编辑闹钟
     *
     * @return
     */
    @PostMapping("/alarm/edit")
    JsonResult<AlarmsDetailResponse> editAlarm(AlarmsEditRequest cubeReqVo);

    /**
     * 删除闹钟
     *
     * @return
     */
    @PostMapping("/alarm/delete")
    JsonResult<AlarmsDetailResponse> deleteAlarm(DeleteAlarmsRequest cubeReqVo);

    /**
     * 获取闹钟详情
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/alarm/get")
    JsonResult<AlarmsDetailResponse> getAlarmDetail(CubeAlarmInfoReqVo reqVo);

    /**
     * 设置开关状态
     *
     * @param request
     * @return
     */
    @PostMapping("/alarm/setEnabled")
    JsonResult<AlarmsDetailResponse> setAlarmEnabled(SetAlarmsEnabledRequest request);



    /**
     * 获取日程列表
     *
     * @return
     */
    @PostMapping("/schedule/list")
    JsonResult<List<UserSchedule>> queryCubeScheduleList(CubeReqVo cubeReqVo);

    /**
     * 新增日程
     *
     * @return
     */
    @PostMapping("/schedule/add")
    JsonResult<UserSchedule> addSchedule(ScheduleAddRequest cubeReqVo);

    /**
     * 编辑日程
     *
     * @return
     */
    @PostMapping("/schedule/edit")
    JsonResult<UserSchedule> editSchedule(ScheduleEditRequest cubeReqVo);

    /**
     * 删除日程
     *
     * @return
     */
    @PostMapping("/schedule/delete")
    JsonResult<UserSchedule> deleteSchedule(DeleteScheduleRequest cubeReqVo);

    /**
     * 获取日程详情
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/schedule/get")
    JsonResult<UserSchedule> getScheduleById(CubeCalendarInfoReqVo reqVo);

    /**
     * 设置开关状态
     *
     * @param request
     * @return
     */
    @PostMapping("/schedule/setEnabled")
    JsonResult<UserSchedule> setScheduleEnabled(SetScheduleEnabledRequest request);

    /**
     * 获取夜灯详情
     */
    @PostMapping("/nightLightConfig/get")
    JsonResult<NightLightConfig> getCubeNightLightInfo(CubeNightLightInfoReqVo reqVo);
    /**
     * 编辑夜灯详情
     */
    @PostMapping("/nightLightConfig/edit")
    JsonResult<NightLightConfig> modifyCubeNightLightInfo(CubeNightLightInfoModifyReqVo reqVo);

    /**
     * 闹钟批量查询
     */
    @PostMapping("alarm/batchListByUuIds")
    JsonResult<List<UserAlarm>> alarmBatchListByUuIds(GetAlarmByUuidsReq req);

    /**
     * 日程批量查询
     */
    @PostMapping("schedule/batchListByUuIds")
    JsonResult<List<UserSchedule>> scheduleBatchListByUuIds(GetScheduleByUuidsReq req);
}
