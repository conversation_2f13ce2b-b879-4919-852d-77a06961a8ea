package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeleteAlarmsRequest {

    /**
     * tal_id
     */
    @NotBlank(message = "talId  cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;

    /**
     * alarmId
     */
    @NotNull(message = "alarmId  cannot be empty")
    private Long alarmId;

    @NotNull(message = "最后修改者  cannot be empty")
    private Integer lastModifiedBy;
}
