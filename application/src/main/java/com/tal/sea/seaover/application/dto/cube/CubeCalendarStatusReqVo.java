package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeCalendarStatusReqVo {
    /**
     * 孩子talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * alarmId
     */
    @NotNull(message = "scheduleId cannot be empty")
    private Long scheduleId;
    /**
     * 日程时间（24 小时制的秒数）
     */
    @NotNull(message = "scheduleTime cannot be empty")
    private Integer scheduleTime;
    /**
     * 重复日 1,5,7 使用逗号分割
     */
    private String repeatDays;
    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 是否启用 0 停用 1 启用
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;
}
