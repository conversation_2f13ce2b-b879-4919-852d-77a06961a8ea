package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeCalendarAddReqVo {
    /**
     * 用户 ID
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 日程名称
     */
    @NotBlank(message = "name cannot be empty")
    private String name;

    /**
     * 日程时间（24 小时制的秒数）
     */
    @NotNull(message = "scheduleTime cannot be empty")
    private Integer scheduleTime;

    /**
     * 提醒时长（1,5,10,20,30 单位分钟）
     */
    @NotNull(message = "notifyDuration cannot be empty")
    private Integer notifyDuration;

    /**
     * 重复日 1,5,7 使用逗号分割
     */
    private String repeatDays;
    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;
    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * label 颜色
     */
    @NotBlank(message = "colour cannot be empty")
    private String colour;

    /**
     * 是否启用（0-否，1-是）
     */
    private Integer enabled;

    /**
     * 是否预置日程（0-否，1-是）
     */
    private Integer isPreSet;
}
