package com.tal.sea.seaover.application.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.*;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.sn.DeviceLocalInfo;
import com.tal.sea.seaover.application.dto.sn.FamilySnChildInfo;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.dto.user.*;
import com.tal.sea.seaover.application.enums.DeviceTypeEnum;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.*;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.service.user.AccountDeleteAsyncService;
import com.tal.sea.seaover.application.service.user.AccountDeleteService;
import com.tal.sea.seaover.application.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.constant.RedisConstant.SN_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccountDeleteServiceImpl implements AccountDeleteService {

    @Autowired
    private TalUserService talUserService;
    @Autowired
    private UserCenterFeign userCenterFeign;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TalParentServerProperties talParentServerProperties;
    @Autowired
    private AlarmXtqService alarmXtqService;
    @Autowired
    private DeviceFeign deviceFeign;
    @Autowired
    private AccountDeleteAsyncService accountDeleteAsyncService;

    @Override
    public AccountDeleteInfo getAccountDeleteInfo(String talId, String os) {
        AccountDeleteInfo info = new AccountDeleteInfo();
        // 1. 获取家长用户信息
        TalUserInfo userInfo = getTalUserInfo(talId, os);
        if (userInfo == null) {
            return null;
        }

        // 2. 获取家庭成员信息
        String familyId = extractFamilyId(userInfo.getFamily_id());

        // 3. 获取学生列表和设备信息
        List<String> childList = getChildList(familyId);
        Map<String, FamilySnChildInfo> snMap = new HashMap<>();
        boolean hasAlarmDevice = processDeviceInfo(familyId, os, talId, snMap);

        // 4. 构建返回结果
        buildResponseItems(info, userInfo, childList, snMap, hasAlarmDevice);
        return info;
    }

    @Override
    public ResultEnum deleteAccountSubmit(String talId, String os) {
        //1 获取家长familyId
        TalUserInfo userInfo = getTalUserInfo(talId, os);
        if (userInfo == null) {
            return ResultEnum.USER_INNER_ERROR;
        }
        String familyId = extractFamilyId(userInfo.getFamily_id());
        if (StrUtil.isBlank(familyId)) {
            //2 家长个人账户注销
            AccountDeleteReqVo reqVo = AccountDeleteReqVo.builder().talId(talId).build();
            JsonResult result = userCenterFeign.familyAccountDestroy(reqVo);
            log.info("家长端删除账号-616家长账户注销, req: {} result: {}", reqVo, result);
            if (!result.isOk()) {
                alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-616家长账户注销提交失败, req: %s result: %s", reqVo, result), null);
                return ResultEnum.USER_INNER_ERROR;
            }
            return ResultEnum.SUCCESS;
        }
        //3 家庭账户注销上报
        AccountDeleteReqVo reqVo = AccountDeleteReqVo.builder().familyId(familyId).talId(talId).build();
        JsonResult result = userCenterFeign.deleteAccountSubmit(reqVo);
        log.info("家长端删除账号-616删除请求提交, req: {} result: {}", reqVo, result);
        if (!result.isOk()) {
            alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-616删除请求提交失败, req: %s result: %s", reqVo, result), null);
            return ResultEnum.USER_INNER_ERROR;
        }
        //4 异步操作设备登出
        accountDeleteAsyncService.asyncLogoutDevice(familyId, talId, os);
        return ResultEnum.SUCCESS;
    }

    @Override
    public ResultEnum deleteAccountActivate(String talId, String os) {
        //1 获取家长familyId
        TalUserInfo userInfo = getTalUserInfo(talId, os);
        if (userInfo == null) {
            return ResultEnum.USER_INNER_ERROR;
        }
        String familyId = extractFamilyId(userInfo.getFamily_id());
        if (StrUtil.isBlank(familyId)) {
            //2 家长个人账户注销
            FamilyLogOutRequestVo reqVo = FamilyLogOutRequestVo.builder().talId(talId).build();
            JsonResult result = userCenterFeign.familyAccountActive(reqVo);
            log.info("家长端删除账号-家长账户激活, req: {} result: {}", reqVo, result);
            if (!result.isOk()) {
                alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-家长账户激活失败, req: %s result: %s", reqVo, result), null);
                return ResultEnum.USER_INNER_ERROR;
            }
            return ResultEnum.SUCCESS;
        }
        //3 家庭激活请求提交
        AccountDeleteReqVo reqVo = AccountDeleteReqVo.builder().familyId(familyId).talId(talId).build();
        JsonResult result = userCenterFeign.deleteAccountActivate(reqVo);
        log.info("家长端删除账号-616激活请求提交, req: {} result: {}", reqVo, result);
        if (!result.isOk()) {
            alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-616删除请求提交失败, req: %s result: %s", reqVo, result), null);
            return ResultEnum.USER_INNER_ERROR;
        }
        return ResultEnum.SUCCESS;
    }

    /**
     * 构造返回结果
     *
     * @param info
     * @param userInfo
     * @param childList
     * @param snMap
     * @param hasAlarmDevice
     */
    private void buildResponseItems(AccountDeleteInfo info, TalUserInfo userInfo, List<String> childList,
                                    Map<String, FamilySnChildInfo> snMap, boolean hasAlarmDevice) {
        info.setParentEmail(userInfo.getEmail());
        List<String> items = new ArrayList<>(talParentServerProperties.getAccountDeleteBeforeItem());

        childList.forEach(child ->
                items.add("Delete " + child + " account"));

        List<FamilySnChildInfo> snChildList = snMap.values().stream().sorted(Comparator.comparing(FamilySnChildInfo::getNickName)).collect(Collectors.toList());
        snChildList.forEach(device ->
                items.add("Delete " + device.getNickName() + "’" + device.getSnName()));

        if (hasAlarmDevice) {
            items.add(talParentServerProperties.getAccountDeleteClockItem());
        }

        info.setItems(items);
    }

    /**
     * 获取学生列表和设备信息
     *
     * @param familyId
     * @param os
     * @param talId
     * @param snMap
     * @return 是否包含闹钟
     */
    private boolean processDeviceInfo(String familyId, String os, String talId, Map<String, FamilySnChildInfo> snMap) {
        if (StrUtil.isBlank(familyId)) {
            return false;
        }
        ServiceResult<TalFamilyUserAndSnInfo> result = talUserService.queryChildAndSnListByFamilyId(familyId, os);
        log.info("processDeviceInfo familyId: {} os: {}, result: {}", familyId, os, result);
        if (!result.isSuccess() || CollectionUtil.isEmpty(result.getData().getHome_list())) {
            return false;
        }

        List<FamilySnChildInfo> processedDevices = result.getData().getHome_list().stream()
                .filter(device -> StrUtil.isNotBlank(device.getDevice_sn()) &&
                        StrUtil.isNotBlank(device.getStu_talid()))
                .map(device -> processSingleDevice(device, familyId))
                .filter(Objects::nonNull)
                .peek(snChildInfo -> updateSnMap(snMap, snChildInfo))
                .collect(Collectors.toList()); // 先收集所有处理后的结果

        // 单独检查是否存在目标设备类型
        return processedDevices.stream()
                .anyMatch(info -> info.getDeviceType() == DeviceTypeEnum.PAWPAL.getValue());
    }

    /**
     * 更新设备信息
     *
     * @param snMap
     * @param newInfo
     */
    private void updateSnMap(Map<String, FamilySnChildInfo> snMap, FamilySnChildInfo newInfo) {
        String key = newInfo.getTalId() + newInfo.getSnModel() + newInfo.getDeviceType();
        FamilySnChildInfo existing = snMap.get(key);

        if (existing == null || (existing != null && !existing.getLoginState())) {
            snMap.put(key, newInfo);
        }
    }

    /**
     * 获取单个设备信息&二次验证设备绑定状态
     *
     * @param device
     * @param familyId
     * @return
     */
    private FamilySnChildInfo processSingleDevice(TalFamilyUserAndSnSubInfo device, String familyId) {
        DeviceLocalInfo localInfo = buildDeviceLocalInfo(device.getDevice_sn());
        if (localInfo.getDeviceType() == null) {
            return null;
        }

        boolean loginState = determineLoginState(device, familyId);
        return FamilySnChildInfo.builder()
                .talId(device.getStu_talid())
                .sn(device.getDevice_sn())
                .nickName(device.getNickname())
                .snName(localInfo.getSnName())
                .snModel(localInfo.getSnModel())
                .deviceType(localInfo.getDeviceType())
                .loginState(loginState)
                .build();
    }

    /**
     * 二次验证设备登录状态
     *
     * @param device
     * @param familyId
     * @return
     */
    private boolean determineLoginState(TalFamilyUserAndSnSubInfo device, String familyId) {
        if (device.getLogin_status() != 1) {
            return false;
        }
        //二次校验设备是否游离
        ServiceResult<TalDeviceSnBindInfo> bindResult =
                talUserService.queryDeviceBySn(device.getDevice_sn());

        return bindResult.isSuccess() &&
                bindResult.getData() != null &&
                familyId.equals(bindResult.getData().getFamily_id());
    }

    /**
     * 获取设备信息
     *
     * @param deviceSn
     * @return
     */
    private DeviceLocalInfo buildDeviceLocalInfo(String deviceSn) {
        DeviceLocalInfo localInfo = new DeviceLocalInfo();
        localInfo.setDeviceSn(deviceSn);
        initDeviceInfo(localInfo);
        return localInfo;
    }

    /**
     * 获取孩子列表
     *
     * @param familyId
     * @return
     */
    private List<String> getChildList(String familyId) {
        if (StrUtil.isBlank(familyId)) {
            return Collections.emptyList();
        }
        ServiceResult<FamilyMemberInfo> result = talUserService.queryFamilyMemberById(familyId);
        log.info("getChildList familyId: {}, result: {}", familyId, result);
        if (!result.isSuccess() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-获取家庭成员失败, familyId: %s", familyId), null);
            return Collections.emptyList();
        }

        return result.getData().getList().stream()
                .filter(member -> member.getFamily_role() == RoleTypeEnum.STUDENT.val())
                .map(FamilyMemberChildInfo::getNickname)
                .collect(Collectors.toList());
    }

    /**
     * 获取家庭ID
     *
     * @param familyIdStr
     * @return
     */
    private String extractFamilyId(String familyIdStr) {
        if (StrUtil.isBlank(familyIdStr)) {
            return null;
        }
        return familyIdStr.split(",")[0];
    }

    private TalUserInfo getTalUserInfo(String talId, String os) {
        ServiceResult<TalUserInfo> result = talUserService.queryUserInfo(talId, os);
        log.info("getTalUserInfo talId: {}, os: {} ,result: {}", talId, os, result);
        if (!result.isSuccess() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-获取家长信息失败, talId: %s", talId), null);
            return null;
        }
        return result.getData();
    }

    /**
     * 初始化设备信息
     *
     * @param localInfo
     */
    private void initDeviceInfo(DeviceLocalInfo localInfo) {
        String redisKey = SN_INFO_KEY + localInfo.getDeviceSn();
        String redisVal = redisService.getString(redisKey);

        if (StrUtil.isNotBlank(redisVal)) {
            populateFromRedis(localInfo, redisVal);
            return;
        }

        fetchFromRemoteService(localInfo, redisKey);
    }

    /**
     * 从Redis中获取设备信息
     *
     * @param localInfo
     * @param redisVal
     */
    private void populateFromRedis(DeviceLocalInfo localInfo, String redisVal) {
        SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
        localInfo.setSnName(Optional.ofNullable(snInfo.getDeviceModel()).orElse(""));
        localInfo.setSnImg(Optional.ofNullable(snInfo.getPictureUrl()).orElse(""));
        localInfo.setSnModel(Optional.ofNullable(snInfo.getDeviceModelName()).orElse(""));
        localInfo.setDeviceType(snInfo.getDeviceType());
    }

    /**
     * 从606获取设备信息
     *
     * @param localInfo
     * @param redisKey
     */
    private void fetchFromRemoteService(DeviceLocalInfo localInfo, String redisKey) {
        JsonResult<SnInfo> result = deviceFeign.queryDeviceInfo(localInfo.getDeviceSn());
        log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", localInfo.getDeviceSn(), result);

        if (result != null && result.getCode() == ResultEnum.SUCCESS.val() && result.getData() != null) {
            SnInfo snInfo = result.getData();
            localInfo.setSnName(Optional.ofNullable(snInfo.getDeviceModel()).orElse(""));
            localInfo.setSnImg(Optional.ofNullable(snInfo.getPictureUrl()).orElse(""));
            localInfo.setSnModel(Optional.ofNullable(snInfo.getDeviceModelName()).orElse(""));
            localInfo.setDeviceType(snInfo.getDeviceType());
            redisService.setString(redisKey, GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
        }
    }
}
