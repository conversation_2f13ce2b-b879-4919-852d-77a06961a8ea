package com.tal.sea.seaover.application.controller.user;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.family.*;
import com.tal.sea.seaover.application.dto.user.GoogleUserInfo;
import com.tal.sea.seaover.application.dto.user.UserInfo;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.CommonService;
import com.tal.sea.seaover.application.service.user.FamilyService;
import com.tal.sea.seaover.application.service.user.UserService;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.config.result.ResultEnum.SUCCESS;
import static com.tal.sea.seaover.application.constant.AppConstant.*;

/**
 * 家庭信息管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/family")
@Tag(name = "家庭信息管理", description = "家庭管理相关接口")
public class FamilyController {

    @Autowired
    FamilyService familyService;

    /**
     * 家庭管理-获取家庭信息
     *
     * @param os
     * @param talId
     * @return
     */
    @Operation(summary = "家庭管理-获取家庭信息", description = "")
    @GetMapping("/info")
    public JsonResult<FamilyInfo> getFamilyInfo(@RequestHeader(value = X_TAL_OS) String os,
                                                @RequestHeader(value = HEADER_TAL_ID) String talId) {
        FamilyInfo familyInfo = familyService.getFamilyInfo(talId, os);
        if (Objects.isNull(familyInfo)) {
            return JsonResult.buildErrorResult("error");
        }
        return JsonResult.buildSuccessResult(familyInfo);
    }

    /**
     * 家庭管理-获取家庭信息v2-兼容闹钟
     *
     * @param os
     * @param talId
     * @return
     */
    @Operation(summary = "家庭管理-获取家庭信息v2", description = "")
    @GetMapping("/v2/info")
    public JsonResult<FamilyInfoV2> getFamilyInfoV2(@RequestHeader(value = X_TAL_OS) String os,
                                                @RequestHeader(value = HEADER_TAL_ID) String talId) {
        FamilyInfoV2 familyInfo = familyService.getFamilyInfoV2(talId, os);
        if (Objects.isNull(familyInfo)) {
            return JsonResult.buildErrorResult("error");
        }
        return JsonResult.buildSuccessResult(familyInfo);
    }

    /**
     * 孩子退出登录
     *
     * @param talId
     * @param reqVo
     * @return
     */
    @Operation(summary = "孩子退出登录", description = "")
    @PostMapping("/child/logout")
    public JsonResult childLogout(@RequestHeader(value = HEADER_TAL_ID) String talId,
                                  @Validated @RequestBody ChildLogOutReqVo reqVo) {
        reqVo.setSn(reqVo.getSn().toUpperCase());
        String errorMsg = familyService.childLogout(talId, reqVo.getChildTalId(), reqVo.getSn());
        if (StrUtil.isNotBlank(errorMsg)) {
            return JsonResult.buildErrorResult(errorMsg);
        }
        return JsonResult.buildSuccessResult(null);
    }

    /**
     * 家庭管理-获取家庭中所有的孩子列表
     *
     * @param os
     * @param talId
     * @return
     */
    @Operation(summary = "家庭管理-获取家庭中所有的孩子列表", description = "")
    @GetMapping("/childList")
    public JsonResult<FamilyChildren> getFamilyChildList(@RequestHeader(value = X_TAL_OS) String os,
                                                         @RequestHeader(value = HEADER_TAL_ID) String talId) {
        return JsonResult.buildSuccessResult(familyService.getFamilyChildList(talId, os));
    }

    /**
     * 家庭管理-新增孩子&加入家庭
     *
     * @param os
     * @param talId
     * @param reqVo
     * @return
     */
    @Operation(summary = "家庭管理-新增孩子&加入家庭", description = "")
    @PostMapping("/addChild")
    public JsonResult<ChildAddRespVo> addChild(@RequestHeader(value = X_TAL_OS) String os,
                                               @RequestHeader(value = HEADER_TAL_ID) String talId,
                                               @Validated @RequestBody ChildAddAndBindReqVo reqVo) {
        ChildAddAndBindRespVo bindResult = familyService.addChild(talId, os, reqVo);
        if (bindResult == null) {
            return JsonResult.buildErrorResult(BIND_INNER_ERROR);
        }
        if (!bindResult.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(bindResult.getResultEnum());
        }
        ChildAddRespVo respVo = ChildAddRespVo.builder()
                .talId(bindResult.getTalId())
                .familyId(bindResult.getFamilyId())
                .build();
        return JsonResult.buildSuccessResult(respVo);
    }

    /**
     * 家庭管理-编辑孩子信息
     *
     * @param os
     * @param talId
     * @param reqVo
     * @return
     */
    @Operation(summary = "家庭管理-编辑孩子信息", description = "")
    @PostMapping("/modifyChild")
    public JsonResult<Void> modifyChild(@RequestHeader(value = X_TAL_OS) String os,
                                        @RequestHeader(value = HEADER_TAL_ID) String talId,
                                        @Validated @RequestBody ChildModifyReqVo reqVo) {
        if (!familyService.modifyChild(talId, os, reqVo)) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        return JsonResult.buildSuccessResult(null);
    }

}
