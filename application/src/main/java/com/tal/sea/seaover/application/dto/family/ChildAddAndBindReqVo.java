package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 请求参数
 * <AUTHOR>
 */
@Data
public class ChildAddAndBindReqVo {
    /**
     * 用户头像
     */
    @NotEmpty(message = "avatarUrl  cannot be empty")
    private String avatarUrl;
    /**
     * 用户昵称
     */
    @NotEmpty(message = "nickname  cannot be empty")
    private String nickname;
    /**
     * 用户年级
     */
    @NotNull(message = "grade  cannot be empty")
    private Integer grade;
    /**
     * 用户性别
     */
    @NotNull(message = "sex  cannot be empty")
    private Integer sex;


}
