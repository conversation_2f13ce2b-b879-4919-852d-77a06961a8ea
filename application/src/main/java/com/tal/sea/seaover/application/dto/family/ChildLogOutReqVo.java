package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChildLogOutReqVo {
    /**
     * 孩子talId
     */
    @Schema(description = "孩子talId", example = "talId")
    @NotEmpty(message = "childTalId  cannot be empty")
    private String childTalId;
    /**
     * 设备sn
     */
    @Schema(description = "设备sn", example = "sdxxxxxxxx")
    @NotEmpty(message = "sn  cannot be empty")
    private String sn;
}
