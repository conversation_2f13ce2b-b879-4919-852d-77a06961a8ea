package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.cube.CubePersonalityInfoVo;
import com.tal.sea.seaover.application.dto.user.DeviceStatusInfo;
import com.tal.sea.seaover.application.dto.user.DeviceStatusInfoRequestVo;
import com.tal.sea.seaover.application.dto.user.SnInfo;
import com.tal.sea.seaover.application.dto.user.SnVersionInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 设备中心
 */
@FeignClient(name = "so-606-device-application", path = "/inner/device")
public interface DeviceFeign {
    /**
     * 查询学习机在线状态
     */
    @PostMapping("/state/query/screen")
    JsonResult<DeviceStatusInfo> queryDeviceStatusInfo(@RequestBody DeviceStatusInfoRequestVo build);

    /**
     * 查询设备信息
     */
    @GetMapping("/query/info")
    JsonResult<SnInfo> queryDeviceInfo(@RequestParam("deviceSn") String deviceSn);

    /**
     * 查询闹钟联网、充电、电量等状态
     */
    @GetMapping("/pawpal/status/query")
    JsonResult<CubePersonalityInfoVo> queryCubePersonalityInfo(@RequestParam("deviceSn") String deviceSn);

    /**
     * 根据设备sn查询系统版本号
     */
    @GetMapping("/version/query/sys")
    JsonResult<SnVersionInfo> queryDeviceVersion(@RequestParam("deviceSn") String deviceSn);
}
