package com.tal.sea.seaover.application.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 请填写表注释
 */
@TableName(value ="dwd_pawpal_alarm_reminder_stat_fd")
@Data
public class CubeHabit {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 设备SN
     */
    private String sn;

    /**
     * 用户ID
     */
    private String talId;

    /**
     * 类型：闹钟/日程
     */
    private Integer type;

    /**
     * 对象ID：闹钟ID/日程ID
     */
    private String objectId;

    /**
     * 当周完成次数
     */
    private Integer weekFinishCnt;

    /**
     * 累计完成次数
     */
    private Integer totalFinishCnt;

    /**
     * 当周完成天数
     */
    private Integer weekFinishDay;

    /**
     * 累计完成天数
     */
    private Integer totalFinishDay;


    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updatedAt;
}
