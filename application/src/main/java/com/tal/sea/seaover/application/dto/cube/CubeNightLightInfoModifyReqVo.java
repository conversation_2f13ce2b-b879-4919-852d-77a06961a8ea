package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeNightLightInfoModifyReqVo {
    /**
     * id
     */
    @NotNull(message = "id cannot be empty")
    private Long id;
    /**
     * 学生talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * 设备sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;
    /**
     * unionid
     */
    //@NotBlank(message = "unionId cannot be empty")
    private String unionId;
    /**
     * 亮度
     */
    @NotNull(message = "brightness cannot be empty")
    private Integer brightness;
    /**
     * 灯效
     */
    @NotNull(message = "lightingEffects cannot be empty")
    private Integer lightingEffects;
    /**
     * 自动照明 0-关闭 1-开启
     */
    //@NotNull(message = "autoLight cannot be empty")
    private Integer autoLight;
    /**
     * 颜色
     */
    @NotBlank(message = "color cannot be empty")
    private String color;
    /**
     * 版本号-时间戳
     */
    @NotNull(message = "version cannot be empty")
    private Long version;
}
