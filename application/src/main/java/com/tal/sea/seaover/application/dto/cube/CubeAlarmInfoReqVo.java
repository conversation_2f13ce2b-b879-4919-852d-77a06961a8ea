package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeAlarmInfoReqVo {
    /**
     * 闹钟id
     */
    @NotNull(message = "alarmId cannot be empty")
    private Long alarmId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;
    /**
     * 孩子talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
}
