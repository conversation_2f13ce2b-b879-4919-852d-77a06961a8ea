package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeInfoReqVo {
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;
    /**
     * 孩子talId
     */
    @NotNull(message = "childTalId cannot be empty")
    private String childTalId;
}
