package com.tal.sea.seaover.application.service.user;

import com.tal.sea.seaover.application.dto.family.*;

/**
 * <AUTHOR>
 */
public interface FamilyService {
    /**
     * 获取家庭信息
     *
     * @param talId
     * @param os
     * @return
     */
    FamilyInfo getFamilyInfo(String talId, String os);

    /**
     * 孩子退出登录
     *
     * @param talId      家长talId
     * @param childTalId 孩子talId
     * @param sn
     */
    String childLogout(String talId, String childTalId, String sn);

    /**
     * 根据孩子talId,查询家长talId
     */
    String queryParentTalIdByChildTalId(String childTalId);

    /**
     * 发送重新查询设备列表接口的push
     */
    boolean sendPushQueryDeviceList(String parentTalId);

    /**
     * 获取家庭中所有的孩子信息
     *
     * @param talId
     * @param os
     * @return
     */
    FamilyChildren getFamilyChildList(String talId, String os);

    /**
     * 新增孩子&绑定家庭
     *
     * @param talId
     * @param os
     * @param reqVo
     * @return
     */
    ChildAddAndBindRespVo addChild(String talId, String os, ChildAddAndBindReqVo reqVo);

    /**
     * 修改孩子
     * @param talId
     * @param os
     * @param reqVo
     * @return
     */
    Boolean modifyChild(String talId, String os, ChildModifyReqVo reqVo);

    FamilyInfoV2 getFamilyInfoV2(String talId, String os);
}
