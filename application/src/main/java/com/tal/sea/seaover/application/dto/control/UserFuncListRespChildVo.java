package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserFuncListRespChildVo {
    @Schema(description = "管控集合key", example = "xxx")
    private String suiteKey;
    @Schema(description = "管控集合名称", example = "xxx")
    private String suiteName;
    @Schema(description = "管控集合描述", example = "xxx")
    private String suiteDesc;

    @Schema(description = "管控集合", example = "xxx")
    private List<ControlRespChildUnitVo> unitList;
}
