package com.tal.sea.seaover.application.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.taluser.ServiceResult;
import com.tal.sea.seaover.application.dto.taluser.TalGoogleUserInfo;
import com.tal.sea.seaover.application.dto.taluser.TalUserInfoPlus;
import com.tal.sea.seaover.application.dto.user.*;
import com.tal.sea.seaover.application.service.common.AlarmXtqService;
import com.tal.sea.seaover.application.service.common.TalSmsService;
import com.tal.sea.seaover.application.service.common.TalUserService;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.service.user.UserService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;

import static com.tal.sea.seaover.application.constant.AppConstant.X_TAL_OS;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserCenterFeign userCenterFeign;
    @Autowired
    private TalUserService talUserService;
    @Autowired
    private AlarmXtqService alarmXtqService;
    @Autowired
    private TalSmsService talSmsService;
    @Autowired
    TalParentServerProperties talParentServerProperties;

    @Override
    public UserInfo codeLogin(String code, String os) {
        JsonResult<CodeLoginCheckResponse> loginCheckResponse = userCenterFeign.codeLogin(CodeLoginRequestVo.builder().code(code).osType("ios".equals(os) ? "2" : "1").build());
        log.info("userCenterFeignInfo 【codeLogin】 : reqCode: {}, os: {} , result:{}", code, os, loginCheckResponse.toString());

        if (loginCheckResponse == null || loginCheckResponse.getCode() != ResultEnum.SUCCESS.val() || loginCheckResponse.getData() == null
                || StrUtil.isBlank(loginCheckResponse.getData().getTalToken())) {
            return null;
        }
        UserInfo userInfo = new UserInfo();
        BeanUtil.copyProperties(loginCheckResponse.getData(), userInfo);
        return userInfo;
    }

    @Override
    public GoogleUserInfo queryUserInfo(String talId, String os) {
        ServiceResult<TalGoogleUserInfo> googleUserInfo = talUserService.queryUserGoogleInfo(talId, os);
        if (!googleUserInfo.isSuccess() || googleUserInfo.getData() == null) {
            return null;
        }
        return GoogleUserInfo.builder()
                .talId(googleUserInfo.getData().getTal_id())
                .avatarUrl(googleUserInfo.getData().getPicture())
                .email(googleUserInfo.getData().getEmail())
                .nickname(googleUserInfo.getData().getName())
                .build();
    }

    @Override
    public void logout(String talId, String os) {
        //1 登出上报用户中心
        ServiceResult result = talUserService.logout(talId, os);
        if (!result.isSuccess()) {
            log.error("parent logout fail, talId: {}, os: {}", talId, os);
            alarmXtqService.alarm("parent logout fail, talId: " + talId + ", os: " + os);
        }
        //2 解绑推送中心
        talSmsService.unregister(talId);
    }

    @Override
    public EmailUserInfo queryUserInfoForEmail(String talId, String os) {
        ServiceResult<TalUserInfoPlus> userInfo = talUserService.queryUserInfoPlus(talId, os);
        if (!userInfo.isSuccess() || userInfo.getData() == null) {
            return null;
        }
        String nickname = userInfo.getData().getNickname();
        //判断是否需要修改昵称
        if (StrUtil.isBlank(userInfo.getData().getNickname())) {
            nickname = "user" + generateRandomString(6);
            ServiceResult<TalUserInfoPlus> modifyResult = talUserService.modifyUserInfo(talId, nickname,  null, null, os);
            if (!modifyResult.isSuccess()) {
                return null;
            }
        }
        return EmailUserInfo.builder()
                .talId(userInfo.getData().getTal_id())
                .avatarUrl(talParentServerProperties.getDefaultAvatar())
                .email(userInfo.getData().getEmail())
                .nickname(nickname)
                .build();
    }

    private String generateRandomString(int length) {
        String CHARSET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random rng = new Random(System.currentTimeMillis());
        char[] chars = new char[length];
        for (int i = 0; i < length; i++) {
            chars[i] = CHARSET.charAt(rng.nextInt(CHARSET.length()));
        }
        return new String(chars);
    }
}
