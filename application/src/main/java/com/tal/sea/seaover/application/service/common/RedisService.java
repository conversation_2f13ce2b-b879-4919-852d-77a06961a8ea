package com.tal.sea.seaover.application.service.common;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisService {


    private final RedissonClient redissonClient;

    @Autowired
    public RedisService(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    // String 操作
    public void setString(String key, String value) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key);
            bucket.set(value);
        } catch (Exception e) {
            log.error("Error setting string: {}", key, e);
        }
    }

    // String 操作
    public void setString(String key, String value, long timeToLive, TimeUnit timeUnit) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key);
            bucket.set(value, timeToLive, timeUnit);
        } catch (Exception e) {
            log.error("Error setting string with TTL: {}", key, e);
        }
    }

    /**
     * 批量获取 Redis 中的值
     *
     * @param keys 需要获取的键列表
     * @return 键值对映射
     */
    public Map<String, String> batchGet(Set<String> keys, String batchKey) {
        // 获取RMap实例
        RMap<String, String> map = redissonClient.getMap(batchKey);
        // 使用getAll方法获取所有指定键的值
        Map<String, String> values = map.getAll(keys);
        return values;
    }

    // Hash 操作
    public void setHash(String key, Map<String, String> map, long timeToLive, TimeUnit timeUnit) {
        try {
            RMapCache<String, String> mapCache = redissonClient.getMapCache(key);
            mapCache.putAll(map, timeToLive, timeUnit);
        } catch (Exception e) {
            log.error("Error setting hash with TTL: {}", key, e);
        }
    }

    // List 操作
    public void addToList(String key, String value, long timeToLive, TimeUnit timeUnit) {
        try {
            RList<String> list = redissonClient.getList(key);
            list.add(value);
            list.expire(timeToLive, timeUnit);
        } catch (Exception e) {
            log.error("Error adding to list with TTL: {}", key, e);
        }
    }

    // Set 操作
    public void addToSet(String key, String value, long timeToLive, TimeUnit timeUnit) {
        try {
            RSet<String> set = redissonClient.getSet(key);
            set.add(value);
            set.expire(timeToLive, timeUnit);
        } catch (Exception e) {
            log.error("Error adding to set with TTL: {}", key, e);
        }
    }

    // Sorted Set (排行榜) 操作
    public void addToSortedSet(String key, String value, double score, long timeToLive, TimeUnit timeUnit) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            sortedSet.add(score, value);
            sortedSet.expire(timeToLive, timeUnit);
        } catch (Exception e) {
            log.error("Error adding to sorted set with TTL: {}", key, e);
        }
    }

    public String getString(String key) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key);
            return bucket.get();
        } catch (Exception e) {
            log.error("Error getting string: {}", key, e);
            return null;
        }
    }

    public void expire(String key, long time, TimeUnit unit) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key);
            bucket.expire(time, unit);
        } catch (Exception e) {
            log.error("Error expiring key: {} in {} {}", key, time, unit, e);
        }
    }

    // Hash 操作
    public void setHash(String key, Map<String, String> map) {
        try {
            RMap<String, String> mapObj = redissonClient.getMap(key);
            mapObj.putAll(map);
        } catch (Exception e) {
            log.error("Error setting hash: {}", key, e);
        }
    }

    public Map<String, String> getHash(String key) {
        try {
            RMap<String, String> mapObj = redissonClient.getMap(key);
            return mapObj.readAllMap();
        } catch (Exception e) {
            log.error("Error getting hash: {}", key, e);
            return null;
        }
    }

    // List 操作
    public void addToList(String key, String value) {
        try {
            RList<String> list = redissonClient.getList(key);
            list.add(value);
        } catch (Exception e) {
            log.error("Error adding to list: {}", key, e);
        }
    }

    public List<String> getList(String key) {
        try {
            RList<String> list = redissonClient.getList(key);
            return list.readAll();
        } catch (Exception e) {
            log.error("Error getting list: {}", key, e);
            return null;
        }
    }

    // Set 操作
    public void addToSet(String key, String value) {
        try {
            RSet<String> set = redissonClient.getSet(key);
            set.add(value);
        } catch (Exception e) {
            log.error("Error adding to set: {}", key, e);
        }
    }

    public Set<String> getSet(String key) {
        try {
            RSet<String> set = redissonClient.getSet(key);
            return set.readAll();
        } catch (Exception e) {
            log.error("Error getting set: {}", key, e);
            return null;
        }
    }

    // Sorted Set (排行榜) 操作
    public void addToSortedSet(String key, String value, double score) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            sortedSet.add(score, value);
        } catch (Exception e) {
            log.error("Error adding to sorted set: {}", key, e);
        }
    }

    public Set<String> getSortedSet(String key) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return (Set<String>) sortedSet.readAll();
        } catch (Exception e) {
            log.error("Error getting sorted set: {}", key, e);
            return null;
        }
    }

    public Set<String> getSortedSetRange(String key, int start, int end) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return (Set<String>) sortedSet.valueRange(start, end);
        } catch (Exception e) {
            log.error("Error getting sorted set range: {}", key, e);
            return null;
        }
    }

    // 分布式锁操作
    public boolean lock(String key, long waitTime, long leaseTime, TimeUnit unit) {
        try {
            RLock lock = redissonClient.getLock(key);
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            log.error("Error acquiring lock: {}", key, e);
            return false;
        }
    }

    public void unlock(String key) {
        try {
            RLock lock = redissonClient.getLock(key);
            lock.unlock();
        } catch (Exception e) {
            log.error("Error releasing lock: {}", key, e);
        }
    }

    // 删除操作
    public void deleteKey(String key) {
        try {
            redissonClient.getKeys().delete(key);
        } catch (Exception e) {
            log.error("Error deleting key: {}", key, e);
        }
    }
}