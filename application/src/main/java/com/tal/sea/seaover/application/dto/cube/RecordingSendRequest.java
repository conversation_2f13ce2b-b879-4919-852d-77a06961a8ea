package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 发送留言请求
 */
@Data
public class RecordingSendRequest implements Serializable {

    /**
     * 留言唯一标识
     */
    @NotBlank(message = "uuid cannot be blank")
    private String uuid;

    @NotBlank(message = "sn cannot be blank")
    private String sn;
}
