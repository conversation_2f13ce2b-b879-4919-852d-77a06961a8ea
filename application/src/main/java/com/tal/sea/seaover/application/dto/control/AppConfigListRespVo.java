package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AppConfigListRespVo {
    @Schema(description = "设备SN号", example = "xxx")
    private String sn;
    @Schema(description = "孩子talId", example = "xxx")
    private String childTalId;
    @Schema(description = "管控项", example = "xxx")
    private List<AppConfigListRespChildVo> list;
}
