package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 保存留言请求
 */
@Data
public class RecordingAddRequest implements Serializable {

    /**
     * 用户ID
     */
    @NotBlank(message = "tailId cannot be blank")
    private String talId;

    /**
     * 设备sn
     */
    @NotBlank(message = "sn cannot be blank")
    private String sn;

    /**
     * 音频文件URL
     */
    @NotBlank(message = "url cannot be blank")
    private String url;

    /**
     * 音频时长(s)
     */
    @NotNull(message = "duration cannot be null")
    @Min(value = 1, message = "duration must be greater than 0")
    private Integer duration;

    /**
     * 录制时间(时间戳)
     */
    @NotNull(message = "recordTime cannot be null")
    private Long recordTime;
}
