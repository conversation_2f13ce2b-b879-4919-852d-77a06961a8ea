package com.tal.sea.seaover.application.controller.sn;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.FamilySnRoleInfo;
import com.tal.sea.seaover.application.dto.sn.*;
import com.tal.sea.seaover.application.service.user.FamilyService;
import com.tal.sea.seaover.application.service.user.SnService;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.constant.AppConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/inner/sn")
@Tag(name = "设备绑定管理", description = "设备管理相关接口")
public class SnInnerController {

    @Autowired
    SnService snService;

    @Autowired
    TalParentServerProperties talParentServerProperties;

    @Autowired
    private FamilyService familyService;


    @PostMapping("/bindSn")
    @Operation(summary = "设备端发起真正绑定接口", description = "校验确认码并绑定孩子设备")
    public JsonResult innerBind(@Validated @RequestBody InnerBindReqVo reqVo) {
        reqVo.setSn(reqVo.getSn().toUpperCase());
        SnCodeCheckAndBindResult bindResult = snService.innerBind(reqVo);
        if (bindResult == null) {
            return JsonResult.buildErrorResult(BIND_INNER_ERROR);
        }
        if (!bindResult.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(bindResult.getResultEnum());
        }
        //绑定成功，发送推送，重新发起设备列表查询push
        CompletableFuture.runAsync(() -> {
            familyService.sendPushQueryDeviceList(reqVo.getParentTalId());
        });
        return JsonResult.buildSuccessResult(null);
    }

    @PostMapping("/device/report")
    public JsonResult deviceReport(@Validated @RequestBody DeviceReportReq reqVo) {
        String parentTalId = familyService.queryParentTalIdByChildTalId(reqVo.getChildTalId());
        if (StrUtil.isBlank(parentTalId)) {
            return JsonResult.buildErrorResult(USER_HAS_NOT_PARENT_ERROR);
        }
        boolean sendState = familyService.sendPushQueryDeviceList(parentTalId);
        if (!sendState) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        return JsonResult.buildSuccessResult(null);
    }
}
