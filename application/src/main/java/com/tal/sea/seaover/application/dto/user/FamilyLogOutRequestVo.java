package com.tal.sea.seaover.application.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyLogOutRequestVo {
    private String talId;
    private String deviceSn;
    private Integer deviceType;
    private List<String> talIdList;
}
