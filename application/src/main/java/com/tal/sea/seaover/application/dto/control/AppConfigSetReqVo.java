package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AppConfigSetReqVo {
    @Schema(description = "pkg名称", example = "xxx")
    @NotBlank(message = "pkgName  cannot be empty")
    private String pkgName;
    @Schema(description = "设备SN号", example = "xxx")
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    @Schema(description = "孩子talId", example = "xxx")
    @NotBlank(message = "childTalId  cannot be empty")
    private String childTalId;

    @Schema(description = "管控集合key", example = "xxx")
    @NotBlank(message = "suiteKey  cannot be empty")
    private String suiteKey;
    @Schema(description = "管控单元项", example = "xxx")
    private List<ConfigSetReqChildVo> unitInfo;
}
