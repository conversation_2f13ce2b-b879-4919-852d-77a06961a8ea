package com.tal.sea.seaover.application.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Configuration
@OpenAPIDefinition(info = @io.swagger.v3.oas.annotations.info.Info(title = "家长端server 项目接口文档", version = "v1.0.0", license = @License(name = "Apache 2.0", url = "https://www.apache.org/licenses/LICENSE-2.0")))
public class Swagger3Config {
//    @Bean
//    public OpenAPI springOpenAPI() {
//        // 访问路径：http://localhost:9090/swagger-ui/index.html
//        // 访问路径：http://localhost:9090/swagger-ui/v3/api-docs
//        return new OpenAPI().info(new Info()
//                .title("家长端接口文档")
//                .description("SpringDoc Simple Application")
//                .version("0.0.1"));
//    }
}
