package com.tal.sea.seaover.application.controller.control;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.control.*;
import com.tal.sea.seaover.application.dto.family.FamilySnRoleInfo;
import com.tal.sea.seaover.application.dto.sn.*;
import com.tal.sea.seaover.application.service.common.TalSmsService;
import com.tal.sea.seaover.application.service.control.ControlService;
import com.tal.sea.seaover.application.service.user.SnService;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.config.result.ResultEnum.SUCCESS;
import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;
import static com.tal.sea.seaover.application.constant.AppConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/control")
@Tag(name = "设备管控", description = "设备管控相关接口")
public class ControlController {

    @Autowired
    ControlService controlService;

    @Autowired
    TalParentServerProperties talParentServerProperties;

    private static final String CONTROL_SCREEN_STOP_USED_TIME_SUIT_KEY = "control_screen_stop_used_time";
    private static final String CONTROL_SCREEN_STOP_USED_TIME_UNIT_KEY = "control_screen_stop_used_time_rule";

    @Operation(summary = "获取用户管控集合列表&管控数据", description = "")
    @PostMapping("/v1/user/func/list")
    public JsonResult<UserFuncListRespVo> userFuncList(@Validated @RequestBody UserFuncListReqVo reqVo) {
        UserFuncListRespVo respVo = controlService.userFuncList(reqVo);
        if (Objects.isNull(respVo)) {
            return JsonResult.buildErrorResult("获取用户管控集合列表&管控数据,reqInfo : " + reqVo);
        }
        return JsonResult.buildSuccessResult(respVo);
    }

    @Operation(summary = "获取应用管控数据", description = "")
    @PostMapping("/v1/app/config/list")
    public JsonResult<AppConfigListRespVo> appConfigList(@Validated @RequestBody AppConfigListReqVo reqVo) {
        AppConfigListRespVo respVo = controlService.appConfigList(reqVo);
        if (Objects.isNull(respVo)) {
            return JsonResult.buildErrorResult("获取应用管控数据,reqInfo : " + reqVo);
        }
        return JsonResult.buildSuccessResult(respVo);
    }

    @Operation(summary = "保存用户管控数据", description = "")
    @PostMapping("/v1/user/config/set")
    public JsonResult userConfigSet(@Validated @RequestBody UserConfigSetReqVo reqVo) {
        Boolean check = SetTimeRepeatCheck(reqVo);
        if (!check) {
            return JsonResult.buildErrorResult("The time period  that you set overlaps an exsiting one to some extent. Please reselect the time.");
        }
        Boolean setFlag = controlService.userConfigSet(reqVo);
        if (!setFlag) {
            log.error("保存用户管控数据失败,reqInfo : {}", reqVo);
            return JsonResult.error();
        }
        return JsonResult.buildSuccessResult(null);
    }


    @Operation(summary = "保存应用管控数据", description = "")
    @PostMapping("/v1/app/config/set")
    public JsonResult appConfigSet(@Validated @RequestBody AppConfigSetReqVo reqVo) {
        Boolean setFlag = controlService.appConfigSet(reqVo);
        if (!setFlag) {
            return JsonResult.buildErrorResult("保存应用管控数据失败");
        }
        return JsonResult.buildSuccessResult(null);
    }


    private Boolean SetTimeRepeatCheck(UserConfigSetReqVo reqVo) {
        try{
            if (!CONTROL_SCREEN_STOP_USED_TIME_SUIT_KEY.equals(reqVo.getSuiteKey())) {
                return true;
            }
            List<ConfigSetReqChildVo> unitInfo = reqVo.getUnitInfo();
            List<String> keyList = unitInfo.stream().map(ConfigSetReqChildVo::getUnitKey).collect(Collectors.toList());
            if (!keyList.contains(CONTROL_SCREEN_STOP_USED_TIME_UNIT_KEY)) {
                return true;
            }
            //停用功能规则 校验
            for (ConfigSetReqChildVo unit : unitInfo) {
                if (!CONTROL_SCREEN_STOP_USED_TIME_UNIT_KEY.equals(unit.getUnitKey())) {
                    continue;
                }
                List<EndTimeSlotVo> endTimeSlotVos = JSONUtil.toList(unit.getUnitValue(), EndTimeSlotVo.class);
                boolean valid = isValid(endTimeSlotVos);
                if (!valid) {
                    return false;
                }
            }
        }catch (Exception e){
            log.error("时间校验出错", e);
        }
        return true;
    }

    public static boolean isValid(List<EndTimeSlotVo> endTimeSlotVos) {
        List<TimeSlot> timeSlots = endTimeSlotVos.stream()
                .map(endTimeSlotVo -> new TimeSlot(endTimeSlotVo))
                .collect(Collectors.toList());

        for (int i = 0; i < timeSlots.size(); i++) {
            for (int j = i + 1; j < timeSlots.size(); j++) {
                if (timeSlots.get(i).overlapsWith(timeSlots.get(j))) {
                    return false;
                }
            }
        }
        return true;
    }
}
