package com.tal.sea.seaover.application.controller.common;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.common.ParentCommonConfig;
import com.tal.sea.seaover.application.dto.common.ParentDeviceData;
import com.tal.sea.seaover.application.dto.common.SecretGetReqVo;
import com.tal.sea.seaover.application.service.common.CommonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;

/**
 * 公共配置等接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/common")
@Tag(name = "公共接口", description = "与业务无关，鉴权等接口")
public class ApiCommonController {

    @Autowired
    private CommonService commonService;
    @Autowired
    private TalParentServerProperties properties;

    /**
     * 获取设备列表
     *
     * @return
     */
    @GetMapping("/getDeviceList")
    public JsonResult<List<ParentDeviceData>> getDeviceList() {
        return JsonResult.buildSuccessResult(properties.getDeviceList());
    }

    /**
     * 获取家长端公共配置信息
     *
     * @return
     */
    @GetMapping("/getCommonConfig")
    public JsonResult<ParentCommonConfig> getCommonConfig() {
        return JsonResult.buildSuccessResult(commonService.getCommonConfig());
    }
}
