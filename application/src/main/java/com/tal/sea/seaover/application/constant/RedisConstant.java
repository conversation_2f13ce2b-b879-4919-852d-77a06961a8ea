package com.tal.sea.seaover.application.constant;   // 解决package

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisConstant {
    /**
     * 父TAL秘钥范围key
     */
    public static final String PARENT_TAL_SECRET_RANGE_KEY = "tal:parent:range:key:";
    /**
     * 用户登录秘钥
     */
    public final static String USER_LOGIN_SECRET_KEY = "tal:parent:secret:range:key:";
    /**
     * sn信息key
     */
    public static final String SN_INFO_KEY = "tal:sn:new:info:key:v2:";
    /**
     * 批量获取key
     */
    public static final String BATCH_GET_KEY_SN = "batchMapKey_SN";

    public static final String SN_BIND_WAITING_KEY = "tal:sn:bind:waiting:key:";

    public static final String SN_BIND_REPEAT_SUCCESS_KEY = "tal:sn:bind:repeat:key:";
}