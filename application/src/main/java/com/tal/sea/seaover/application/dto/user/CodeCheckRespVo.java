package com.tal.sea.seaover.application.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeCheckRespVo {
    //1:  code正确 2：code错误 3：code格式错误
    private Integer codeValid;
    private String parentTalId;
    private String childTalId;
    private String deviceSn;
}
