package com.tal.sea.seaover.application.enums;

/**
 * cube项目修改来源
 * 1-device，2-parent
 *
 * <AUTHOR>
 */
public enum CubeSourceEnum {

    DEVICE(1, "未知"),
    PARENT(2, "家长");

    ;

    private final int val;
    private final String msg;

    CubeSourceEnum(int val, String msg) {
        this.val = val;
        this.msg = msg;
    }

    public int val() {
        return this.val;
    }

    public String msg() {
        return this.msg;
    }
}
