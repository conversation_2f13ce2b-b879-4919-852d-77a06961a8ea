package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.sms.UnregisterByUserRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 消息中心推送服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "so-601-public-application", path = "/inner/public")
public interface SmsCenterFeign {

    /**
     * 推送服务
     */
    @PostMapping(value = "/push/send")
    JsonResult pushMsg(@RequestBody @Validated PushParam param);

    /**
     * 推送服务
     */
    @PostMapping(value = "/push/unregister")
    JsonResult unregister(@RequestBody @Validated UnregisterByUserRequest param);
}
