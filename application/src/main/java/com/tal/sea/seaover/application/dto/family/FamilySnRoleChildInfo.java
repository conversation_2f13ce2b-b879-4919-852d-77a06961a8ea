package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilySnRoleChildInfo {

    @Schema(description = "类别ID", example = "user123")
    private Integer id;

    @Schema(description = "中文名称", example = "爸爸、妈妈等")
    private String chineseName;

    @Schema(description = "英文名称", example = "father、mather等")
    private String englishName;
}