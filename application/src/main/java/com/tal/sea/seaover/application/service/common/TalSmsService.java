package com.tal.sea.seaover.application.service.common;   // 解决package
// 默认导入lombok，方便日志打印


import com.google.gson.reflect.TypeToken;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.sms.UnregisterByUserRequest;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.service.feign.SmsCenterFeign;
import com.tal.sea.seaover.application.util.GsonUtil;
import com.tal.sea.xpod.tools.util.OkHttpHelperUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.List;


@Slf4j
@Service
public class TalSmsService {
    @Autowired
    private SmsCenterFeign smsCenterFeign;
    @Autowired
    private TalParentServerProperties properties;

    @Autowired
    private AlarmXtqService alarmXtqService;

    public Boolean sendMessage(List<String> userIds, Integer pushType, String bizType) {
        TalParentServerProperties.SmsPushData pushData = properties.getPushMessages().get(bizType);
        if (pushData == null) {
            log.error("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: {}, pushType: {}, bizType: {}", userIds, pushType, bizType);
            alarmXtqService.alarm(String.format("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: %s, pushType: %s, bizType: %s", userIds, pushType, bizType));
            return false;
        }
        PushParam pushParam = PushParam.builder()
                .appId(pushData.getAppId())
                .type(pushType)
                .userIds(userIds)
                .data(PushParam.PushData.builder()
                        .title(pushData.getTitle())
                        .body(pushData.getBody())
                        .data(pushData.getData())
                        .build())
                .build();
        try {
            JsonResult jsonResult = smsCenterFeign.pushMsg(pushParam);
            log.info("smsCenterFeignInfo 【push/send】 : reqVo: {}, result:{}", pushParam, jsonResult);
            if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
                log.error("家长端发送消息失败, userIds: {}, pushType: {}, bizType: {}, pushParam: {} ,result :{}", userIds, pushType, bizType, GsonUtil.toJson(pushParam), jsonResult);
                alarmXtqService.alarm(String.format("家长端发送消息失败, userIds: %s, pushType: %s, bizType: %s, pushParam: %s ,result :%s", userIds, pushType, bizType, GsonUtil.toJson(pushParam), jsonResult));
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
            log.error("家长端发送消息失败, userIds: {}, pushType: {}, bizType: {}, pushParam: {} ,errorMsg :{}", userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMessage, e);
            alarmXtqService.alarm(String.format("家长端发送消息失败, userIds: %s, pushType: %s, bizType: %s, pushParam: %s ,errorMsg :%s", userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMessage));
        }
        return false;
    }

    /**
     * 封装消息发送
     *
     * @param userIds  用户id列表
     * @param pushType 推送类型 参见 {@link PushParam.SmsTypeEnum}
     * @param bizType  业务类型 参见 {@link TalParentServerProperties.SmsBizEnum}
     */
    public Boolean sendMessageWithRetry(List<String> userIds, Integer pushType, String bizType) {
        TalParentServerProperties.SmsPushData pushData = properties.getPushMessages().get(bizType);
        if (pushData == null) {
            log.error("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: {}, pushType: {}, bizType: {}", userIds, pushType, bizType);
            alarmXtqService.alarm(String.format("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: %s, pushType: %s, bizType: %s", userIds, pushType, bizType));
            return false;
        }
        PushParam pushParam = buildPushParam(pushData, userIds, pushType);
        int maxRetries = properties.getPushMsgRetryMaxCount();
        int retryCount = 0;
        long initialDelay = 1000; // 初始延迟1秒
        long maxDelay = 10000;    // 最大延迟10秒
        while (retryCount <= maxRetries) {
            try {
                JsonResult jsonResult = smsCenterFeign.pushMsg(pushParam);
                log.info("smsCenterFeignInfo 【push/send】 : reqVo: {} 第{}次发送结果 result:{}", pushParam, retryCount + 1, jsonResult);

                if (jsonResult != null && jsonResult.getCode() == ResultEnum.SUCCESS.val()) {
                    return true; // 发送成功直接返回
                }
                // 触发重试条件：结果为空或业务失败
                if (retryCount < maxRetries) {
                    long delay = calculateExponentialBackoff(retryCount, initialDelay, maxDelay);
                    log.warn("发送失败，准备第{}次重试，等待{}ms", retryCount + 1, delay);
                    Thread.sleep(delay);
                }
            } catch (Exception e) {
                if (retryCount == maxRetries) {
                    String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
                    log.error("家长端发送消息重试{}次后最终失败, userIds: {}, pushType: {}, bizType: {}, pushParam: {} ,errorMsg :{}", maxRetries, userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMessage, e);
                    alarmXtqService.alarm(buildAlarmMsg(userIds, pushType, bizType, pushParam, e));
                    return false;
                }
                // 异常时自动进入下次重试
            }
            retryCount++;
        }
        // 重试耗尽后记录日志
        log.error("消息发送重试耗尽，最终失败. userIds: {}, bizType: {}", userIds, bizType);
        alarmXtqService.alarm(buildAlarmMsg(userIds, pushType, bizType, pushParam, null));
        return false;
    }

    /**
     * 封装消息发送
     *
     * @param userIds  用户id列表
     * @param pushType 推送类型 参见 {@link PushParam.SmsTypeEnum}
     * @param bizType  业务类型 参见 {@link TalParentServerProperties.SmsBizEnum}
     * @param body     推送内容
     */
    public Boolean sendMessage(List<String> userIds, Integer pushType, String bizType, String body) {
        TalParentServerProperties.SmsPushData pushData = properties.getPushMessages().get(bizType);
        if (pushData == null) {
            log.error("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: {}, pushType: {}, bizType: {}", userIds, pushType, bizType);
            alarmXtqService.alarm(String.format("家长端发送消息失败, 发送类型未配置, 请检查配置信息 userIds: %s, pushType: %s, bizType: %s", userIds, pushType, bizType));
            return false;
        }
        PushParam pushParam = PushParam.builder()
                .appId(pushData.getAppId())
                .type(pushType)
                .userIds(userIds)
                .data(PushParam.PushData.builder()
                        .title(pushData.getTitle())
                        .body(pushData.getBody())
                        .data(body)
                        .build())
                .build();
        try {
            JsonResult jsonResult = smsCenterFeign.pushMsg(pushParam);
            log.info("smsCenterFeignInfo 【push/send】 : reqVo: {}, result:{}", pushParam, jsonResult);
            if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
                log.error("家长端发送消息失败, userIds: {}, pushType: {}, bizType: {}, pushParam: {} ,result :{}", userIds, pushType, bizType, GsonUtil.toJson(pushParam), jsonResult);
                alarmXtqService.alarm(String.format("家长端发送消息失败, userIds: %s, pushType: %s, bizType: %s, pushParam: %s ,result :%s", userIds, pushType, bizType, GsonUtil.toJson(pushParam), jsonResult));
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
            log.error("家长端发送消息失败, userIds: {}, pushType: {}, bizType: {}, pushParam: {} ,errorMsg :{}", userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMessage, e);
            alarmXtqService.alarm(String.format("家长端发送消息失败, userIds: %s, pushType: %s, bizType: %s, pushParam: %s ,errorMsg :%s", userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMessage));
        }
        return false;
    }


    /**
     * 解绑客户端
     *
     * @param talId 用户id
     */
    public void unregister(String talId) {
        try {
            JsonResult jsonResult = smsCenterFeign.unregister(UnregisterByUserRequest.builder().talId(talId).deviceSn("ParentApp").build());
            log.info("smsCenterFeignInfo 【unregister】, talId: {}, result :{}", talId, jsonResult);
            if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
                log.error("家长端解绑消息中心失败, talId: {}, result :{}", talId, jsonResult);
                alarmXtqService.alarm(String.format("家长端解绑消息中心失败, talId: %s, result :%s", talId, jsonResult));
            }
        } catch (Exception e) {
            String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
            log.error("家长端解绑消息中心失败, talId: {}, result :{}", talId, errorMessage, e);
            alarmXtqService.alarm(String.format("家长端解绑消息中心失败, talId: %s,errorMsg :%s", talId, errorMessage));
        }
    }


    // 指数退避计算（含随机抖动避免惊群）
    private long calculateExponentialBackoff(int retryCount, long initialDelay, long maxDelay) {
        long delay = (long) (initialDelay * Math.pow(2, retryCount));
        delay = Math.min(delay, maxDelay);
        // 添加±20%随机抖动
        long jitter = (long) (delay * 0.2 * Math.random());
        return (long) (delay - jitter + Math.random() * 2 * jitter);
    }

    private String buildAlarmMsg(List<String> userIds, Integer pushType, String bizType,
                                 PushParam pushParam, Exception e) {
        String errorMsg = e != null ? e.getMessage() : "result null/failed";
        return String.format("家长端发送消息失败, userIds: %s, pushType: %s, bizType: %s, pushParam: %s, errorMsg: %s",
                userIds, pushType, bizType, GsonUtil.toJson(pushParam), errorMsg);
    }

    private PushParam buildPushParam(TalParentServerProperties.SmsPushData pushData, List<String> userIds, Integer pushType) {
        return PushParam.builder()
                .appId(pushData.getAppId())
                .type(pushType)
                .userIds(userIds)
                .data(PushParam.PushData.builder()
                        .title(pushData.getTitle())
                        .body(pushData.getBody())
                        .data(pushData.getData())
                        .build())
                .build();
    }


}