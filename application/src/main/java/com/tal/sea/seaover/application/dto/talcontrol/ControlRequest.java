package com.tal.sea.seaover.application.dto.talcontrol;   // 解决package

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Data
@Builder
public class ControlRequest {
    private String tal_id;
    private String sn;
    private String pkg_name;

    private String scence_code;

    public List<String> suit_key;
    public List<String> unite_key;
    public List<String> pkg_names;
    public List<ControlSubRequest> info;
    private Integer cg;//用户退出登录上报时默认传94
}