package com.tal.sea.seaover.application.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class CodeLoginReqVo {
    /**
     * udc获取的code
     */
    @NotEmpty(message = "code  cannot be empty")
    private String code;
}
