package com.tal.sea.seaover.application.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindDeviceReqVo {
    private String deviceSn;
    private String parentTalId;
    private String familyId;
    private Integer bindingStatus;//绑定状态 1: 未绑定  2: 绑定中  3:绑定完成

    public enum BindStatusEnum {
        //绑定状态 1: 未绑定  2: 绑定中  3:预校验成功
        UNBIND(1), BINDING(2), BIND(3);
        private int status;

        BindStatusEnum(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }


}
