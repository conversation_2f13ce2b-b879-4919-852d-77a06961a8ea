package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyChildInfoBody {
    /**
     * 设备sn号
     */
    @Schema(description = "登录的设备sn号", example = "12345667")
    private String sn;
    /**
     * 设备型号
     */
    @Schema(description = "登录的设备型号", example = "T100")
    private String snModel;
    /**
     * 设备名称
     */
    @Schema(description = "登录的设备名称", example = "Thinkpal Tablet T100")
    private String snName;
    /**
     * 设备图片
     */
    @Schema(description = "登录的设备sn图片", example = "http://111111.jpg")
    private String snImg;
    /**
     * 设备类型 0 学习机 1 闹钟cube
     */
    @Schema(description = "设备类型 0 学习机 1 闹钟cube")
    private Integer deviceType;
}