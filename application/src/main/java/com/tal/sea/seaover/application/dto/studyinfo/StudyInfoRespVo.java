package com.tal.sea.seaover.application.dto.studyinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StudyInfoRespVo {
    /** tal_id */
    @Schema(description = "tal_id")
    private String talId;
    /** 型号 */
    @Schema(description = "型号")
    private String model;
    /** 本周起始时间 */
    @Schema(description = "本周起始时间")
    private String weekDate;
    /** 提交题目数 */
    @Schema(description = "提交题目数")
    private Integer questionCnt;
    /** 学习知识点数 */
    @Schema(description = "学习知识点数")
    private Integer knowledgeCnt;
    /** 完成任务数 */
    @Schema(description = "完成任务数")
    private Integer taskCnt;
    /** 使用总时长（秒） */
    @Schema(description = "使用总时长（秒）")
    private Integer totalDuration;
    /** 系统课时长（秒） */
    @Schema(description = "系统课时长（秒）")
    private Integer systemDuration;
    /** 精准学时长（秒） */
    @Schema(description = "精准学时长（秒）")
    private Integer accurateDuration;
    /** 阅读时长（秒） */
    @Schema(description = "阅读时长（秒）")
    private Integer readingDuration;
    /** 学习工具时长（秒） **/
    @Schema(description = "学习工具时长（秒）")
    private Integer toolDuration;
    /** 精准学答题正确率 **/
    @Schema(description = "精准学答题正确率")
    private Integer jzxQuestionCorrectRate;
    /** 游戏时长（秒） **/
    @Schema(description = "游戏时长")
    private Integer gameLearnDuration;
}
