package com.tal.sea.seaover.application.dto.sn;   // 解决package
// 默认导入lombok，方便日志打印

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description:
 * @author: zhouyanhuang
 * @date: 2025/2/13 21:49
 * @Version: 1.0
 */
@Slf4j
@Data
public class DeviceReportReq {
    @NotEmpty(message = "childTalId  cannot be empty")
    private String childTalId;
}