package com.tal.sea.seaover.application.config.exception;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import lombok.Data;

/**
 * @title: BaseException
 * @Description: 基础异常
 * @author: cheng
 */
@Data
public class BaseException extends RuntimeException {

    private int code = ResultEnum.ERROR.val();
    private String msg = ResultEnum.ERROR.msg();

    public BaseException() {
        super();
    }

    public BaseException(String message) {
        super(message);
        this.msg = message;
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.msg = message;
    }

    public BaseException(int code, String message) {
        super(message);
        this.code = code;
        this.msg = message;
    }

    public BaseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.msg = message;
    }
}
