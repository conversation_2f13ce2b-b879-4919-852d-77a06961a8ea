package com.tal.sea.seaover.application.service.user;


import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.AccountDeleteInfo;

/**
 * <AUTHOR>
 */
public interface AccountDeleteService {

    AccountDeleteInfo getAccountDeleteInfo(String talId, String os);

    ResultEnum deleteAccountSubmit(String talId, String os);

    ResultEnum deleteAccountActivate(String talId, String os);
}
