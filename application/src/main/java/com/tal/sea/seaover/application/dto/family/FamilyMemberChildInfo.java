package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class FamilyMemberChildInfo {

    private String family_role_name;
    private String avatar_url;
    private String sex;
    private Integer family_role;
    private String nickname;
    private String hide_phone;
    private String family_id;
    private Long created_at;
    private Integer is_manager;
    private String grade;
    private String tal_id;
}