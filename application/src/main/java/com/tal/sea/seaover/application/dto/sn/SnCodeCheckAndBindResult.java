package com.tal.sea.seaover.application.dto.sn;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SnCodeCheckAndBindResult {
    //绑定结果枚举
    ResultEnum resultEnum;

    String resultMsg;

}
