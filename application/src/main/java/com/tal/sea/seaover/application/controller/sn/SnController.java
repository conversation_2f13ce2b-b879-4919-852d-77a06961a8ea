package com.tal.sea.seaover.application.controller.sn;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.ChildLogOutReqVo;
import com.tal.sea.seaover.application.dto.family.FamilySnRoleInfo;
import com.tal.sea.seaover.application.dto.sn.*;
import com.tal.sea.seaover.application.dto.user.GoogleUserInfo;
import com.tal.sea.seaover.application.dto.user.UserInfo;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.CommonService;
import com.tal.sea.seaover.application.service.user.SnService;
import com.tal.sea.seaover.application.service.user.UserService;
import com.tal.sea.seaover.application.service.version.TbParentAppVersionService;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.constant.AppConstant.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/sn")
@Tag(name = "设备绑定管理", description = "设备管理相关接口")
public class SnController {

    @Autowired
    SnService snService;

    @Autowired
    TalParentServerProperties talParentServerProperties;
    @Resource
    TbParentAppVersionService tbParentAppVersionService;

    /**
     * 获取家庭设备列表信息
     * @param request
     * @param os
     * @param talId
     * @return
     */
    @Operation(summary = "获取家庭设备列表信息", description = "")
    @GetMapping("/familySnInfo")
    public JsonResult<FamilySnInfo> familySnInfo(HttpServletRequest request,@RequestHeader(value = X_TAL_OS) String os,
                                                 @RequestHeader(value = HEADER_TAL_ID) String talId) {
        String traceId = ThreadMdcUtil.getTraceId();
        tbParentAppVersionService.report(request,traceId);
        FamilySnInfo familySnInfo = snService.queryFamilySnInfo(talId, os);
        if (Objects.isNull(familySnInfo)) {
            return JsonResult.buildErrorResult("查询家庭设备列表失败,talId: " + talId);
        }
        return JsonResult.buildSuccessResult(familySnInfo);
    }

    @PostMapping("/snCheck")
    @Operation(summary = "校验SN码接口", description = "校验SN码接口")
    @ApiResponse(responseCode = "200", description = "成功")
    @ApiResponse(responseCode = "101001", description = "内部错误")
    @ApiResponse(responseCode = "101101", description = "SN码错误")
    @ApiResponse(responseCode = "616154", description = "This account is logged in on the device")
    @ApiResponse(responseCode = "616155", description = "sn production is not initialized")
    @ApiResponse(responseCode = "616156", description = "The device is not beating at this time")
    public JsonResult snCheck(@RequestHeader(value = HEADER_TAL_ID) String talId,
                              @RequestHeader(value = X_TAL_OS) String os,
                              @Validated @RequestBody SnCheckReqVo reqVo) {
        reqVo.setSn(reqVo.getSn().toUpperCase());
        ResultEnum resultEnum = snService.snCheck(talId, reqVo.getSn(), os);
        if (!resultEnum.equals(SUCCESS)) {
            return JsonResult.buildErrorResult(resultEnum);
        }
        return JsonResult.buildSuccessResult(null);
    }


    @PostMapping("/codeCheckAndBind")
    @Operation(summary = "校验确认码并绑定孩子设备", description = "校验确认码并绑定孩子设备")
    @ApiResponse(responseCode = "200", description = "成功获取用户信息")
    @ApiResponse(responseCode = "101001", description = "内部错误")
    @ApiResponse(responseCode = "101201", description = "Confirmation code error")
    @ApiResponse(responseCode = "101202", description = "binding status error")
    public JsonResult codeCheckAndBind(@RequestHeader(value = X_TAL_OS) String os,
                                       @RequestHeader(value = HEADER_TAL_ID) String talId,
                                       @Validated @RequestBody CodeCheckBindReqVo reqVo) {
        reqVo.setParentTalId(talId);
        reqVo.setOs(os);
        reqVo.setSn(reqVo.getSn().toUpperCase());
        SnCodeCheckAndBindResult bindResult = snService.codeCheckAndBind(reqVo);
        if (bindResult == null) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        if (!bindResult.getResultEnum().equals(SUCCESS)) {
            if (StrUtil.isBlank(bindResult.getResultMsg())) {
                return JsonResult.buildErrorResult(bindResult.getResultEnum());
            }else {
                return JsonResult.buildErrorResultWithMsg(bindResult.getResultEnum(),bindResult.getResultMsg());
            }
        }
        return JsonResult.buildSuccessResult(null);
    }

    @PostMapping("/v2/codeCheckAndBind")
    @Operation(summary = "校验确认码并绑定孩子设备", description = "校验确认码并绑定孩子设备")
    @ApiResponse(responseCode = "200", description = "成功获取用户信息")
    @ApiResponse(responseCode = "101001", description = "内部错误")
    @ApiResponse(responseCode = "101201", description = "Confirmation code error")
    @ApiResponse(responseCode = "101202", description = "binding status error")
    public JsonResult codeCheckAndBindV2( @RequestHeader(value = X_TAL_OS) String os,
                                       @RequestHeader(value = HEADER_TAL_ID) String talId,
                                       @Validated @RequestBody CodeCheckBindReqVo reqVo) {
        reqVo.setParentTalId(talId);
        reqVo.setOs(os);
        reqVo.setSn(reqVo.getSn().toUpperCase());
        SnCodeCheckAndBindResult bindResult = snService.codeCheckAndBindV2(reqVo);
        if (bindResult == null) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        if (!bindResult.getResultEnum().equals(SUCCESS)) {
            if (StrUtil.isBlank(bindResult.getResultMsg())) {
                return JsonResult.buildErrorResult(bindResult.getResultEnum());
            }else {
                return JsonResult.buildErrorResultWithMsg(bindResult.getResultEnum(),bindResult.getResultMsg());
            }
        }
        return JsonResult.buildSuccessResult(null);
    }


    @Operation(summary = "获取家庭身份范围信息", description = "")
    @GetMapping("/getFamilyRoles")
    public JsonResult<FamilySnRoleInfo> getFamilyRoles() {
        return JsonResult.buildSuccessResult(FamilySnRoleInfo.builder().roles(talParentServerProperties.getFamilyRoles()).build());
    }


    @PostMapping("/updateFamilyRoleInfo")
    @Operation(summary = "更新家长身份信息", description = "更新家长身份信息")
    public JsonResult updateFamilyRoleInfo( @RequestHeader(value = X_TAL_OS) String os,
                                           @RequestHeader(value = HEADER_TAL_ID) String talId,
                                           @Validated @RequestBody UpdateFamilyRoleReqVo reqVo) {
        ResultEnum updateEnum = snService.updateFamilyRoleInfo(talId, reqVo.getRoleId(), os);
        if (updateEnum == null) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        if (!updateEnum.equals(SUCCESS)) {
            return JsonResult.buildErrorResult(updateEnum);
        }
        return JsonResult.buildSuccessResult(null);
    }

}
