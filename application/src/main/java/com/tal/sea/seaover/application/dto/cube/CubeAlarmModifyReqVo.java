package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class CubeAlarmModifyReqVo {
    /**
     * 闹钟ID
     */
    @NotNull(message = "id  cannot be empty")
    private Long id;
    /**
     * tal_id
     */
    @NotBlank(message = "talId  cannot be empty")
    private String talId;

    /**
     * 闹钟名称
     */
    @NotBlank(message = "alarmName cannot be empty")
    private String alarmName;
    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;

    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId  cannot be empty")
    private String unionId;

    /**
     * 闹钟时间（秒级时间）
     */
    @NotNull(message = "alarmTime  cannot be empty")
    private Integer alarmTime;
    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;
    /**
     * 重复日（格式 1,5,6）
     */
    private String repeatDays;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * 铃声 ID
     */
    private String ringId;

    /**
     * 是否启用（0 - 否，1 - 是）
     */
    private Integer enabled;

    /**
     * 是否预置闹钟（0 - 否，1 - 是）
     */
    private Integer isPreSet;
}
