package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 定时发送留言请求
 */
@Data
public class RecordingScheduledSendRequest implements Serializable {

    /**
     * 留言唯一标识
     */
    @NotBlank(message = "uuid cannot be blank")
    private String uuid;

    /**
     * 定时发送时间戳
     */
    @NotNull(message = "scheduledTime cannot be null")
    private Long scheduledTime;
}
