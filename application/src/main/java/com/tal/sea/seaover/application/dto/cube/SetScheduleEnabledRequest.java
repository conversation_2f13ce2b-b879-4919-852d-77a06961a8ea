package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class SetScheduleEnabledRequest {
    /**
     * alarmId
     */
    @NotNull(message = "scheduleId  cannot be empty")
    private Long scheduleId;

    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "repeating  cannot be empty")
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * 是否启用
     */
    @NotBlank(message = "enabled  cannot be empty")
    private Integer enabled;
    /**
     * 最后修改人
     */
    @NotBlank(message = "lastModifiedBy  cannot be empty")
    private Integer lastModifiedBy;
}
