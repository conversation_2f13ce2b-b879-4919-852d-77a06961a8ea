package com.tal.sea.seaover.application.dto.taluser;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class TalUserInfoPlus {
    private Integer uid;

    private String tal_id;

    private Integer sex;

    private String tal_name;

    private String realname;

    private String udc_nickname;

    private String client_nickname;

    private String nickname;

    private String avator_url;

    private String email;

    private String hide_phone;

    private String school_year;

    private Integer grade;

    private String grade_name;

    private Integer stage;

    private String stage_name;

    private String province;

    private String province_name;

    private String city;

    private String city_name;

    private String county;

    private String county_name;

    private String birthday;

    private Integer create_time;

    private Integer role_type;

    private String family_id;

    private List<TalUserInfoPlusChild> family_data;

    private String skin_id;

    private String skin_component;

    private String skin_icon;

    private Integer is_factory_user;

    private String introduction;

    private Integer tal_type;

    private Object tags;

    private Boolean is_new;
}
