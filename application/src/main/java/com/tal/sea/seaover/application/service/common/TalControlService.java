package com.tal.sea.seaover.application.service.common;


import com.google.gson.reflect.TypeToken;
import com.tal.sea.seaover.application.dto.control.AppConfigSetReqVo;
import com.tal.sea.seaover.application.dto.control.ConfigSetReqChildVo;
import com.tal.sea.seaover.application.dto.control.UserConfigSetReqVo;
import com.tal.sea.seaover.application.dto.talcontrol.*;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.util.GsonUtil;
import com.tal.sea.xpod.tools.util.OkHttpHelperUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户管控中心-用户管控
 */
@Slf4j
@Service
public class TalControlService {

    @Value("${user.control-url}")
    private String baseUserPath;
    @Value("${user.device-url}")
    private String devicePath;

    @Autowired
    private AlarmXtqService alarmXtqService;

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private final OkHttpClient client = OkHttpHelperUtil.createClient(10);

    private static final String PARENT_SCENCE_CODE = "azure-parent-t100";

    /**
     * 【用户管控中心】获取用户管控数据
     */
    public ServiceResult<UserControl> getUserConfigInfo(String talId,String sn) {
        String url = baseUserPath + "/control-svc/user/config/list";
        ControlRequest controlRequest = ControlRequest.builder().tal_id(talId).sn(sn).scence_code(PARENT_SCENCE_CODE).build();
        Type responseType = new TypeToken<TalUserCommonResponse<UserControl>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(controlRequest), responseType);
    }

    /**
     * 【用户管控中心】获取设备应用列表的管控
     */
    public ServiceResult<AppControl> getAppConfigInfo(String sn,List<String> pkgNames) {
        String url = baseUserPath + "/control-svc/device-app/config/list";
        ControlRequest controlRequest = ControlRequest.builder().sn(sn).scence_code(PARENT_SCENCE_CODE).pkg_names(pkgNames).build();
        Type responseType = new TypeToken<TalUserCommonResponse<AppControl>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(controlRequest), responseType);
    }
    /**
     * 【用户管控中心】获取设备应用列表
     */
    public ServiceResult<AppBeanControl> getAppList(String sn) {
        String url = devicePath + "/device-svc/app/ctr-list";
        ControlRequest controlRequest = ControlRequest.builder().sn(sn).build();
        Type responseType = new TypeToken<TalUserCommonResponse<AppBeanControl>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(controlRequest), responseType);
    }

    /**
     * 【用户管控中心】用户配置提交
     */
    public ServiceResult userConfigSet(UserConfigSetReqVo reqVo) {
        String url = baseUserPath + "/control-svc/user/config/batch-set";
        List<ControlSubRequest> info = new ArrayList<>();
        for (ConfigSetReqChildVo reqChildVo : reqVo.getUnitInfo()) {
            info.add(ControlSubRequest.builder().suite_key(reqVo.getSuiteKey())
                    .unit_key(reqChildVo.getUnitKey())
                    .unit_value(reqChildVo.getUnitValue()).build());
        }
        ControlRequest controlRequest = ControlRequest.builder().tal_id(reqVo.getChildTalId())
                .sn(reqVo.getSn()).info(info).build();
        Type responseType = new TypeToken<TalUserCommonResponse>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(controlRequest), responseType);
    }

    /**
     * 【用户管控中心】应用配置提交
     */
    public ServiceResult appConfigSet(AppConfigSetReqVo reqVo) {
        String url = baseUserPath + "/control-svc/device-app/config/batch-set";
        List<ControlSubRequest> info = new ArrayList<>();
        for (ConfigSetReqChildVo reqChildVo : reqVo.getUnitInfo()) {
            info.add(ControlSubRequest.builder().suite_key(reqVo.getSuiteKey())
                    .unit_key(reqChildVo.getUnitKey())
                    .unit_value(reqChildVo.getUnitValue()).build());
        }
        ControlRequest controlRequest = ControlRequest.builder().tal_id(reqVo.getChildTalId())
                .sn(reqVo.getSn()).pkg_name(reqVo.getPkgName()).info(info).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalUserInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(controlRequest), responseType);
    }


    private <T> ServiceResult<T> callTalUserService(String url, String reqJsonStr, Type responseType) {
        try {
            RequestBody requestBody = RequestBody.create(JSON, reqJsonStr);
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("callUserServiceLog info url :{}  reqStr:{} result: {}", url, reqJsonStr, responseBody);

                TalUserCommonResponse<T> commonResponse = GsonUtil.fromJsonByType(responseBody, responseType);
                if (commonResponse != null && commonResponse.getErrcode() == 0) {
                    return new ServiceResult<>(commonResponse.getData(), true);
                } else {
                    log.error("callUserServiceLog url :{}  error reqJsonStr: {} return:{}", url, reqJsonStr, responseBody);
                    alarmXtqService.alarm(String.format("调用用户管控中心结果返回失败,url: %s, reqJsonStr: %s, result: %s", url, reqJsonStr, responseBody));
                    return new ServiceResult<>(null, false);
                }
            } else {
                throw new RuntimeException("response is not success");
            }
        } catch (Exception e) {
            log.error("callUserServiceLog error url :{} reqJsonStr: {} return:{}", url, reqJsonStr, e.getMessage(), e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
            alarmXtqService.alarm(String.format("调用用户管控中心失败,url: %s, reqJsonStr: %s, error: %s", url, reqJsonStr, errorMessage));
        }
        return new ServiceResult<>(null, false);
    }


}