package com.tal.sea.seaover.application.controller.user;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.user.*;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.CommonService;
import com.tal.sea.seaover.application.service.user.UserService;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;
import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_TYPE_ERROR;
import static com.tal.sea.seaover.application.constant.AppConstant.*;

/**
 * 用户信息管理
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@Tag(name = "用户信息管理", description = "用户管理相关接口")
public class UserController {

    @Autowired
    UserService userService;
    @Autowired
    CommonService commonService;

    @Operation(summary = "获取家长信息", description = "")
    @GetMapping("/info")
    public JsonResult<GoogleUserInfo> queryUserInfo( @RequestHeader(value = X_TAL_OS) String os,
                                                    @RequestHeader(value = HEADER_TAL_ID) String talId) {
        GoogleUserInfo userInfo = userService.queryUserInfo(talId, os);
        if (Objects.isNull(userInfo)) {
            return JsonResult.buildErrorResult("查询用户失败,talId: " + talId);
        }
        return JsonResult.buildSuccessResult(userInfo);
    }

    @Operation(summary = "获取家长信息-获取邮箱账户相关信息", description = "")
    @GetMapping("/v2/info")
    public JsonResult<EmailUserInfo> queryUserInfoV2(@RequestHeader(value = X_TAL_OS) String os,
                                                     @RequestHeader(value = HEADER_TAL_ID) String talId) {
        EmailUserInfo userInfo = userService.queryUserInfoForEmail(talId, os);
        if (Objects.isNull(userInfo)) {
            return JsonResult.buildErrorResult("查询用户失败,talId: " + talId);
        }
        return JsonResult.buildSuccessResult(userInfo);
    }

    @PostMapping("/codeLogin")
    @Operation(summary = "根据udc-code码进行登录", description = "根据udc-code码进行登录")
    @ApiResponse(responseCode = "200", description = "成功获取用户信息")
    @ApiResponse(responseCode = "101001", description = "内部错误")
    @ApiResponse(responseCode = "101002", description = "使用孩子角色无法在家长端登录")
    public JsonResult<UserInfo> codeLogin(@RequestHeader(value = X_TAL_OS) String os,
                                          @Validated @RequestBody CodeLoginReqVo reqVo) {
        UserInfo userInfo = userService.codeLogin(reqVo.getCode(), os);
        if (userInfo == null || StrUtil.isBlank(userInfo.getTalId()) || StrUtil.isBlank(userInfo.getTalToken())) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        if (userInfo.getFamilyRole() != null && userInfo.getFamilyRole() == RoleTypeEnum.STUDENT.val()) {
            return JsonResult.buildErrorResult(USER_TYPE_ERROR);
        }
        //登录成功后将访问秘钥同时发给前端
        String secret = commonService.getSecret(userInfo.getTalId());
        String[] s = secret.split("_");
        userInfo.setSecretStart(Integer.parseInt(s[0]));
        userInfo.setSecretEnd(Integer.parseInt(s[1]));
        return JsonResult.buildSuccessResult(userInfo);
    }


    /**
     * 根据udc-code码进行登录v2接口
     * 新增账号状态（1：正常 2: 待删除）
     * @param os
     * @param reqVo
     * @return
     */
    @PostMapping("/v2/codeLogin")
    public JsonResult<UserInfo> codeLoginV2(@RequestHeader(value = X_TAL_OS) String os,
                                              @Validated @RequestBody CodeLoginReqVo reqVo) {
        UserInfo userInfo = userService.codeLogin(reqVo.getCode(), os);
        if (userInfo == null || StrUtil.isBlank(userInfo.getTalId()) || StrUtil.isBlank(userInfo.getTalToken())) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        if (userInfo.getFamilyRole() != null && userInfo.getFamilyRole() == RoleTypeEnum.STUDENT.val()) {
            return JsonResult.buildErrorResult(USER_TYPE_ERROR);
        }
        //登录成功后将访问秘钥同时发给前端
        String secret = commonService.getSecret(userInfo.getTalId());
        String[] s = secret.split("_");
        userInfo.setSecretStart(Integer.parseInt(s[0]));
        userInfo.setSecretEnd(Integer.parseInt(s[1]));
        return JsonResult.buildSuccessResult(userInfo);
    }


    @Operation(summary = "家长退出登录信息上报", description = "")
    @PostMapping("/logout")
    public JsonResult logout(@RequestHeader(value = X_TAL_OS) String os,
                             @RequestHeader(value = HEADER_TAL_ID) String talId) {
        userService.logout(talId, os);
        return JsonResult.buildSuccessResult(null);
    }

}
