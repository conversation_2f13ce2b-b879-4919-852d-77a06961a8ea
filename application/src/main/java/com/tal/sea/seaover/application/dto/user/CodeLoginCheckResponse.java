package com.tal.sea.seaover.application.dto.user;   // 解决package

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 登陆接口接口
 */
@Slf4j
@Data
@Builder
public class CodeLoginCheckResponse {
    private String talToken;

    private String talId;

    private String talName;

    private String nickname;

    private String avatarUrl;

    private Integer sex;

    //在家庭中的身份：0-未知 1-妈妈 2-爸爸 3-爷爷 4-奶奶 5-外公 6-外婆 7-祖父 8-祖母 98-家人 99-孩子
    private Integer familyRole;

    /**
     * 账号状态（1：正常 2: 待删除）
     */
    private Integer status;

}