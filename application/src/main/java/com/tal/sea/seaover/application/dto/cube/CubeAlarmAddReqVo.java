package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class CubeAlarmAddReqVo {

    /**
     * 孩子tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 闹钟名称
     */
    @NotBlank(message = "alarmName cannot be empty")
    private String alarmName;
    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * 闹钟时间（秒级时间）
     */
    @NotNull
    private Long alarmTime;

    /**
     * 重复日（格式 1,5,6）
     */
    private String repeatDays;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * 铃声 ID
     */
    @NotBlank(message = "ringId cannot be empty")
    private String ringId;

    /**
     * 是否启用（0 - 否，1 - 是）
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;
}
