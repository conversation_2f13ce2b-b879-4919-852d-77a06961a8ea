package com.tal.sea.seaover.application.service.cube;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.cube.*;
import com.tal.sea.seaover.application.dto.sn.SnCodeCheckAndBindResult;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.dto.user.ScanQrCodeResponse;
import com.tal.sea.seaover.application.dto.user.SnInfo;
import com.tal.sea.seaover.application.dto.user.SnInfoRequestVo;
import com.tal.sea.seaover.application.enums.CubeSourceEnum;
import com.tal.sea.seaover.application.service.common.AlarmXtqService;
import com.tal.sea.seaover.application.service.common.RedisService;
import com.tal.sea.seaover.application.service.common.TalCubeService;
import com.tal.sea.seaover.application.service.common.TalUserService;
import com.tal.sea.seaover.application.service.feign.CubeControlServerFeign;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.constant.RedisConstant.SN_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CubeService {

    @Autowired
    UserCenterFeign userCenterFeign;
    @Autowired
    private TalParentServerProperties parentServerProperties;
    @Autowired
    private AlarmXtqService alarmXtqService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DeviceFeign deviceFeign;
    @Autowired
    private TalUserService talUserService;
    @Autowired
    private CubeControlServerFeign cubeControlServerFeign;
    @Autowired
    private TalCubeService talCubeService;
    /**
     * 下游重复错误返回二维码
     */
    private static Long CUBE_REPEAT_ERROR_CODE = 703001L;

    public QrCodeCheckRespVo checkQrCode(QrCodeCheckReqVo reqVo, String talId, String os) {
        //1 查询家长邮箱
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(talId, os);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("家长端闹钟扫描校验接口失败-获取家长信息失败, reqVo: %s result: %s", reqVo, userInfoData), null);
        }
        reqVo.setParentEmail(userInfoData.getData().getEmail());
        //2 进行扫码校验
        JsonResult<ScanQrCodeResponse> jsonResult = userCenterFeign.checkQrCode(reqVo);
        log.info("cubeAuthServerFeignInfo 【checkQrCode】 : reqVo: {}, result:{}", reqVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            log.error("家长端闹钟扫描校验接口失败, reqVo: {} result: {}", reqVo, jsonResult);
            return null;
        }
        QrCodeCheckRespVo respVo = new QrCodeCheckRespVo();
        BeanUtil.copyProperties(jsonResult.getData(), respVo);
        return respVo;
    }

    public Boolean cancelBind(CubeCancelBindReqVo reqVo) {
        JsonResult<Boolean> jsonResult = userCenterFeign.cancelBind(reqVo);
        log.info("userCenterFeignInfo 【cancelBind】 : reqVo: {}, result:{}", reqVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val() || jsonResult.getData() == null || !jsonResult.getData()) {
            log.error("家长端二维码取消调用接口失败, reqVo: {} result: {}", reqVo, jsonResult);
            return false;
        }
        return jsonResult.getData();
    }


    public CubeBindAndLoginInnerRespVo bindAndLogin(CubeBindAndLoginReqVo reqVo) {
        //1 调用周帆确认登录接口
        JsonResult<Boolean> confirmResult = userCenterFeign.cubeScanConfirm(reqVo);
        log.info("userCenterFeignInfo 【cubeScanConfirm】 : reqVo: {}, result:{}", reqVo, confirmResult);
        if (confirmResult == null || confirmResult.getCode() != ResultEnum.SUCCESS.val() || confirmResult.getData() == null || !confirmResult.getData()) {
            alarmXtqService.alarmAndLogForError(String.format("家长端绑定cube扫码确认调用失败, reqVo: %s result: %s", reqVo, confirmResult), null);
            return CubeBindAndLoginInnerRespVo.builder().resultEnum(ResultEnum.CUBE_BIND_DEFAULT_ERROR).build();
        }
        return CubeBindAndLoginInnerRespVo.builder().resultEnum(ResultEnum.SUCCESS).build();
    }

    public CubeInfoRespVo getCubeInfo(CubeInfoReqVo reqVo, String parentTalId) {
        CubeInfoRespVo device = new CubeInfoRespVo();
        //1 查询基础信息
        initDeviceBasicInfo(device, reqVo, parentTalId);
        //2 查询动态数据【联网状态、电量】
        JsonResult<CubePersonalityInfoVo> jsonResult = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("deviceFeignInfo 【queryCubePersonalityInfo】 : reqVo: {}, result:{}", reqVo.getSn(), jsonResult);
        if (jsonResult != null && jsonResult.getCode() == ResultEnum.SUCCESS.val() && jsonResult.getData() != null) {
            device.setOnlineState(jsonResult.getData().getWifiStatus() != null && jsonResult.getData().getWifiStatus() == 1);
            device.setBattery(jsonResult.getData().getCurrentElectricity());
            device.setChargeState(jsonResult.getData().getChargingStatus());
        } else {
            alarmXtqService.alarmAndLogForError(String.format("查询cube设备动态数据失败, reqVo: %s result: %s", reqVo, jsonResult), null);
        }
        //3 查询已开启的闹钟数量
        CubeAlarmCountReqVo alarmCountReqVo = CubeAlarmCountReqVo.builder().talId(reqVo.getChildTalId()).build();
        JsonResult<Integer> alarmCountJsonResult = cubeControlServerFeign.queryCubeAlarmCount(alarmCountReqVo);
        log.info("cubeControlServerFeignInfo 【queryCubeAlarmCount】 : reqVo: {}, result:{}", alarmCountReqVo, alarmCountJsonResult);
        if (alarmCountJsonResult == null || alarmCountJsonResult.getCode() != ResultEnum.SUCCESS.val() || alarmCountJsonResult.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube设备已开启闹钟动态数据失败, reqVo: %s result: %s", alarmCountReqVo, alarmCountJsonResult), null);
        } else {
            device.setAlarmCount(alarmCountJsonResult.getData());
        }
        //4 查询已开启的日程数量
        CubeScheduleCountReqVo scheduleCountReqVo = CubeScheduleCountReqVo.builder().talId(reqVo.getChildTalId()).build();
        JsonResult<Integer> scheduleCountJsonResult = cubeControlServerFeign.queryCubeScheduleCount(scheduleCountReqVo);
        log.info("cubeControlServerFeignInfo 【queryCubeScheduleCount】 : reqVo: {}, result:{}", scheduleCountReqVo, scheduleCountJsonResult);
        if (scheduleCountJsonResult == null || scheduleCountJsonResult.getCode() != ResultEnum.SUCCESS.val() || scheduleCountJsonResult.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube设备已开启日程动态数据失败, reqVo: %s result: %s", scheduleCountReqVo, alarmCountJsonResult), null);
        } else {
            device.setScheduleCount(scheduleCountJsonResult.getData());
        }
        return device;
    }

    private void initDeviceBasicInfo(CubeInfoRespVo device, CubeInfoReqVo reqVo, String parentTalId) {
        String redisKey = SN_INFO_KEY + reqVo.getSn();
        String redisVal = redisService.getString(redisKey);
        if (StrUtil.isNotBlank(redisVal)) {
            SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
            device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
        } else {
            SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(reqVo.getSn()).parentTalId(parentTalId).build();
            JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(requestVo.getDeviceSn());
            log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", requestVo, snInfoJsonResult);
            if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
                SnInfo snInfo = snInfoJsonResult.getData();
                device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
                device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
                device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
                redisService.setString(redisKey, GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
            }
        }
    }

    public CubeAlarmListRespVo getCubeAlarmList(CubeAlarmListReqVo reqVo) {
        CubeAlarmListRespVo respVo = new CubeAlarmListRespVo();
        CubeReqVo cubeReqVo = CubeReqVo.builder().talId(reqVo.getTalId()).build();
        JsonResult<List<CubeAlarmRespVo>> listJsonResult = cubeControlServerFeign.queryCubeAlarmList(cubeReqVo);
        log.info("cubeControlServerFeignInfo 【queryCubeAlarmList】 : reqVo: {}, result:{}", cubeReqVo, listJsonResult);
        if (listJsonResult == null || listJsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube闹钟列表数据失败, reqVo: %s result: %s", cubeReqVo, listJsonResult), null);
            return respVo;
        }
        if (listJsonResult.getData() == null || CollectionUtil.isEmpty(listJsonResult.getData())) {
            return respVo;
        }
        List<CubeAlarmListChildRespVo> list = new ArrayList<>();
        listJsonResult.getData().forEach(item -> {
            list.add(initCubeAlarmListChildRespVo(item));
        });
        respVo.setList(list);
        return respVo;
    }

    private CubeAlarmListChildRespVo initCubeAlarmListChildRespVo(CubeAlarmRespVo item) {
        CubeAlarmListChildRespVo CubeChildAlarm = new CubeAlarmListChildRespVo();
        BeanUtil.copyProperties(item, CubeChildAlarm);
        CubeChildAlarm.setRepeatDays(CubeChildAlarm.getRepeating() == 1 ? item.getRepeatDays() : "");
        CubeChildAlarm.setRepeatDaysDescription(getAlarmRepeatDescription(CubeChildAlarm.getRepeatDays()));
        return CubeChildAlarm;
    }

//    public static String getAlarmRepeatDescription(String repeatDaysStr) {
//        if (repeatDaysStr == null || repeatDaysStr.isEmpty()) {
//            return "Never";
//        }
//
//        // 将字符串转换为整数数组
//        String[] parts = repeatDaysStr.split(",");
//        int[] repeatDays = new int[parts.length];
//        for (int i = 0; i < parts.length; i++) {
//            repeatDays[i] = Integer.parseInt(parts[i].trim());
//        }
//
//        // 对数组进行排序
//        Arrays.sort(repeatDays);
//
//        // 去重
//        Set<Integer> daySet = new HashSet<>();
//        for (int day : repeatDays) {
//            daySet.add(day);
//        }
//        int[] uniqueDays = daySet.stream().mapToInt(Number::intValue).toArray();
//        Arrays.sort(uniqueDays);
//
//        // 定义星期缩写
//        String[] weekDays = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
//
//        // 特殊情况处理
//        // 检查是否选择了全部7天
//        if (uniqueDays.length == 7 && uniqueDays[0] == 1 && uniqueDays[6] == 7) {
//            return "everyday";
//        }
//
//        // 检查是否选择了周六和周日 (6和7)
//        if (uniqueDays.length == 2 && uniqueDays[0] == 6 && uniqueDays[1] == 7) {
//            return "every weekend";
//        }
//
//        // 检查是否选择了周一到周五 (1-5)
//        if (uniqueDays.length == 5 && uniqueDays[0] == 1 && uniqueDays[4] == 5) {
//            return "every weekday";
//        }
//
//        // 构建结果字符串
//        StringBuilder result = new StringBuilder();
//        for (int i = 0; i < uniqueDays.length; i++) {
//            // 将1-7转换为对应的缩写，注意输入1对应的是周一，7对应周日
//            int dayIndex = uniqueDays[i] - 1; // 调整索引，使输入1对应Mon（索引1）
//            result.append(weekDays[dayIndex]);
//
//            if (i < uniqueDays.length - 1) {
//                result.append(", ");
//            }
//        }
//        return result.toString();
//    }

    public static String getAlarmRepeatDescription(String repeatDaysStr) {
        if (repeatDaysStr == null || repeatDaysStr.isEmpty()) {
            return "Never";
        }

        // 将字符串转换为整数集合（去重）
        Set<Integer> daySet = new HashSet<>();
        for (String part : repeatDaysStr.split(",")) {
            if (!part.trim().isEmpty()) {
                int day = Integer.parseInt(part.trim());
                // 将7映射到0（表示周日），其他保持不变
                day = day == 7 ? 0 : day;
                daySet.add(day);
            }
        }

        // 转换为数组并排序
        Integer[] repeatDays = daySet.toArray(new Integer[0]);
        Arrays.sort(repeatDays);

        // 特殊情况处理
        // 检查是否选择了全部7天
        if (daySet.size() == 7) {
            return "everyday";
        }

        // 检查是否选择了周六和周日（6和7映射为6和0）
        if (daySet.size() == 2 &&
                ((daySet.contains(6) && daySet.contains(0)))) {
            return "every weekend";
        }

        // 检查是否选择了周一到周五（1-5）
        boolean isWeekdays = true;
        for (int day : daySet) {
            if (day < 1 || day > 5) {
                isWeekdays = false;
                break;
            }
        }
        if (isWeekdays && daySet.size() == 5) {
            return "every weekday";
        }

        // 定义星期顺序（周日为第一天）
        String[] weekDays = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};

        // 构建结果字符串
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < repeatDays.length; i++) {
            // 将0映射回周日
            int day = repeatDays[i];
            String dayStr;
            if (day == 0) {
                dayStr = weekDays[0];
            } else {
                dayStr = weekDays[day];
            }
            result.append(dayStr);

            if (i < repeatDays.length - 1) {
                result.append(", ");
            }
        }
        return result.toString();
    }

    public CubeRepeatCheckRespVo addCubeAlarm(CubeAlarmAddReqVo reqVo) {
        AlarmsAddRequest alarmsAddRequest = initAlarmAddRequest(reqVo);
        JsonResult<AlarmsDetailResponse> cubeAlarmListResult = cubeControlServerFeign.addAlarm(alarmsAddRequest);
        log.info("cubeControlServerFeign.addAlarm 【addCubeAlarm】 : reqVo: {}, result:{}", alarmsAddRequest, cubeAlarmListResult);
        if (cubeAlarmListResult != null && cubeAlarmListResult.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("新增cube闹钟失败-闹钟时间重复, reqVo: %s result: %s", alarmsAddRequest, cubeAlarmListResult));
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_TIME_CONFLICT).build();
        }
        if (cubeAlarmListResult == null || cubeAlarmListResult.getCode() != ResultEnum.SUCCESS.val() || cubeAlarmListResult.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("新增cube闹钟失败, reqVo: %s result: %s", alarmsAddRequest, cubeAlarmListResult), null);
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckRespVo.builder().resultEnum(SUCCESS).id(cubeAlarmListResult.getData().getId()).build();
    }

    private AlarmsAddRequest initAlarmAddRequest(CubeAlarmAddReqVo reqVo) {
        AlarmsAddRequest alarmsAddRequest = new AlarmsAddRequest();
        BeanUtil.copyProperties(reqVo, alarmsAddRequest);
        alarmsAddRequest.setUnionId(UUID.randomUUID().toString());
        alarmsAddRequest.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (alarmsAddRequest.getRepeating() == 0) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            alarmsAddRequest.setAlarmDay(calculateAlarmDate(alarmsAddRequest.getAlarmTime()));
//        }
        alarmsAddRequest.setEnabled(1);
        alarmsAddRequest.setIsPreSet(0);
        alarmsAddRequest.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return alarmsAddRequest;
    }

//    public static String calculateAlarmDate(int alarmSeconds) {
//        LocalDateTime now = LocalDateTime.now();
//
//        // 获取当前时间的秒数（从当天0点开始计算）
//        int currentSeconds = now.getHour() * 3600 + now.getMinute() * 60 + now.getSecond();
//
//        LocalDateTime alarmDateTime;
//        if (alarmSeconds < currentSeconds) {
//            // 如果闹钟时间大于当前时间的秒数，则日期是明天
//            alarmDateTime = now.plusDays(1);
//        } else {
//            // 否则日期是今天
//            alarmDateTime = now;
//        }
//        log.info("calculateAlarmDate: current: {} alarmSecond:{}", currentSeconds, alarmSeconds);
//        // 返回格式化后的日期字符串
//        return alarmDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//    }

    public CubeRepeatCheckRespVo modifyCubeAlarm(CubeAlarmModifyReqVo reqVo) {
        AlarmsEditRequest request = initAlarmEditRequest(reqVo);
        JsonResult<AlarmsDetailResponse> editAlarmResult = cubeControlServerFeign.editAlarm(request);
        log.info("cubeControlServerFeign.editAlarm 【modifyCubeAlarm】 : reqVo: {}, result:{}", request, editAlarmResult);
        if (editAlarmResult != null && editAlarmResult.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("修改cube闹钟失败-闹钟时间重复, reqVo: %s result: %s", request, editAlarmResult));
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_TIME_CONFLICT).build();
        }
        if (editAlarmResult == null || editAlarmResult.getCode() != ResultEnum.SUCCESS.val() || editAlarmResult.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("修改cube闹钟失败, reqVo: %s result: %s", request, editAlarmResult), null);
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckRespVo.builder().resultEnum(SUCCESS).modifyResult(true).build();
    }

    private AlarmsEditRequest initAlarmEditRequest(CubeAlarmModifyReqVo reqVo) {
        AlarmsEditRequest editRequest = new AlarmsEditRequest();
        BeanUtil.copyProperties(reqVo, editRequest);
        editRequest.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (editRequest.getRepeating() == 0) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            editRequest.setAlarmDay(calculateAlarmDate(editRequest.getAlarmTime()));
//        }
        editRequest.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return editRequest;
    }

    public Boolean deleteCubeAlarm(CubeAlarmDeleteReqVo reqVo) {
        DeleteAlarmsRequest request = initAlarmDeleteRequest(reqVo);
        JsonResult<AlarmsDetailResponse> deleteAlarmResult = cubeControlServerFeign.deleteAlarm(request);
        log.info("cubeControlServerFeign.deleteAlarm 【deleteCubeAlarm】 : reqVo: {}, result:{}", request, deleteAlarmResult);
        if (deleteAlarmResult == null || deleteAlarmResult.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("删除cube闹钟失败, reqVo: %s result: %s", request, deleteAlarmResult), null);
            return null;
        }
        return true;
    }

    private DeleteAlarmsRequest initAlarmDeleteRequest(CubeAlarmDeleteReqVo reqVo) {
        DeleteAlarmsRequest request = new DeleteAlarmsRequest();
        BeanUtil.copyProperties(reqVo, request);
        request.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return request;
    }

    public CubeAlarmInfoRespVo getCubeAlarmInfo(CubeAlarmInfoReqVo reqVo) {
        JsonResult<AlarmsDetailResponse> alarmResult = cubeControlServerFeign.getAlarmDetail(reqVo);
        log.info("cubeControlServerFeign.getAlarmDetail 【getCubeAlarmInfo】 : reqVo: {}, result:{}", reqVo, alarmResult);
        if (alarmResult == null || alarmResult.getCode() != ResultEnum.SUCCESS.val() || alarmResult.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("获取cube闹钟详情失败, reqVo: %s result: %s", reqVo, alarmResult), null);
            return null;
        }
        CubeAlarmInfoRespVo respVo = new CubeAlarmInfoRespVo();
        BeanUtil.copyProperties(alarmResult.getData(), respVo);
        respVo.setRepeatDays(respVo.getRepeating() == 1 ? respVo.getRepeatDays() : "");
        return respVo;
    }

    public CubeRepeatCheckRespVo modifyCubeAlarmStatus(CubeAlarmStatusReqVo reqVo) {
        SetAlarmsEnabledRequest request = initAlarmStatusRequest(reqVo);
        JsonResult<AlarmsDetailResponse> result = cubeControlServerFeign.setAlarmEnabled(request);
        log.info("cubeControlServerFeign.setAlarmEnabled 【modifyCubeAlarmStatus】 : reqVo: {}, result:{}", request, result);
        if (result != null && result.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("修改cube闹钟状态失败-闹钟时间重复, reqVo: %s result: %s", request, result));
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_TIME_CONFLICT).build();
        }
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("修改cube闹钟失败, reqVo: %s result: %s", request, result), null);
            return CubeRepeatCheckRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckRespVo.builder().resultEnum(SUCCESS).modifyResult(result.getData() != null ? true : false).build();
    }

    private SetAlarmsEnabledRequest initAlarmStatusRequest(CubeAlarmStatusReqVo reqVo) {
        SetAlarmsEnabledRequest request = new SetAlarmsEnabledRequest();
        BeanUtil.copyProperties(reqVo, request);
        request.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (StrUtil.isNotBlank(reqVo.getRepeatDays())) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            request.setAlarmDay(calculateAlarmDate(reqVo.getAlarmTime()));
//        }
        request.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return request;
    }

    public CubeCalendarListRespVo getCubeCalendarList(CubeCalendarListReqVo reqVo) {
        CubeCalendarListRespVo respVo = new CubeCalendarListRespVo();
        CubeReqVo cubeReqVo = CubeReqVo.builder().talId(reqVo.getTalId()).build();
        JsonResult<List<UserSchedule>> listJsonResult = cubeControlServerFeign.queryCubeScheduleList(cubeReqVo);
        log.info("cubeControlServerFeign.queryCubeScheduleList 【getCubeCalendarList】 : reqVo: {}, result:{}", cubeReqVo, listJsonResult);
        if (listJsonResult == null || listJsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube日程列表数据失败, reqVo: %s result: %s", cubeReqVo, listJsonResult), null);
            return respVo;
        }
        if (listJsonResult.getData() == null || CollectionUtil.isEmpty(listJsonResult.getData())) {
            return respVo;
        }
        List<CubeCalendarListChildRespVo> list = new ArrayList<>();
        listJsonResult.getData().forEach(item -> {
            list.add(initCubeCalendarListChildRespVo(item));
        });
        respVo.setList(list);
        return respVo;
    }

    private CubeCalendarListChildRespVo initCubeCalendarListChildRespVo(UserSchedule item) {
        CubeCalendarListChildRespVo respVo = new CubeCalendarListChildRespVo();
        BeanUtil.copyProperties(item, respVo);
        respVo.setRepeatDays(respVo.getRepeating() == 1 ? respVo.getRepeatDays() : "");
        return respVo;
    }

    public CubeRepeatCheckCalendarRespVo addCubeCalendar(CubeCalendarAddReqVo reqVo) {
        ScheduleAddRequest request = initScheduleAddRequest(reqVo);
        JsonResult<UserSchedule> result = cubeControlServerFeign.addSchedule(request);
        log.info("cubeControlServerFeign.addSchedule 【addCubeCalendar】 : reqVo: {}, result:{}", request, result);
        if (result != null && result.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("新增cube日程失败-时间重复, reqVo: %s result: %s", request, result));
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_CALENDAR_TIME_CONFLICT).build();
        }
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("新增cube日程失败, reqVo: %s result: %s", request, result), null);
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckCalendarRespVo.builder().resultEnum(SUCCESS).id(result.getData().getId()).build();
    }

    private ScheduleAddRequest initScheduleAddRequest(CubeCalendarAddReqVo reqVo) {
        ScheduleAddRequest request = new ScheduleAddRequest();
        BeanUtil.copyProperties(reqVo, request);
        request.setUnionId(UUID.randomUUID().toString());
        request.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (request.getRepeating() == 0) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            request.setScheduleDay(calculateAlarmDate(request.getScheduleTime()));
//        }
        request.setEnabled(1);
        request.setIsPreSet(0);
        request.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return request;
    }

    public CubeRepeatCheckCalendarRespVo modifyCubeCalendar(CubeCalendarModifyReqVo reqVo) {
        ScheduleEditRequest request = initCalendarEditRequest(reqVo);
        JsonResult<UserSchedule> result = cubeControlServerFeign.editSchedule(request);
        log.info("cubeControlServerFeign.editSchedule 【modifyCubeCalendar】 : reqVo: {}, result:{}", request, result);
        if (result != null && result.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("修改cube日程失败-时间重复, reqVo: %s result: %s", request, result));
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_CALENDAR_TIME_CONFLICT).build();
        }
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("修改cube日程失败, reqVo: %s result: %s", request, result), null);
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckCalendarRespVo.builder().resultEnum(SUCCESS).modifyResult(true).build();
    }

    private ScheduleEditRequest initCalendarEditRequest(CubeCalendarModifyReqVo reqVo) {
        ScheduleEditRequest editRequest = new ScheduleEditRequest();
        BeanUtil.copyProperties(reqVo, editRequest);
        editRequest.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (editRequest.getRepeating() == 0) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            editRequest.setScheduleDay(calculateAlarmDate(editRequest.getScheduleTime()));
//        }
        editRequest.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return editRequest;
    }

    public Boolean deleteCubeCalendar(CubeCalendarDeleteReqVo reqVo) {
        DeleteScheduleRequest request = initScheduleDeleteRequest(reqVo);
        JsonResult<UserSchedule> result = cubeControlServerFeign.deleteSchedule(request);
        log.info("cubeControlServerFeign.deleteSchedule 【deleteCubeCalendar】 : reqVo: {}, result:{}", request, result);
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("删除cube日程失败, reqVo: %s result: %s", request, result), null);
            return null;
        }
        return true;
    }

    private DeleteScheduleRequest initScheduleDeleteRequest(CubeCalendarDeleteReqVo reqVo) {
        DeleteScheduleRequest request = new DeleteScheduleRequest();
        BeanUtil.copyProperties(reqVo, request);
        request.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return request;
    }

    public CubeCalendarInfoRespVo getCubeCalendarInfo(CubeCalendarInfoReqVo reqVo) {
        JsonResult<UserSchedule> result = cubeControlServerFeign.getScheduleById(reqVo);
        log.info("cubeControlServerFeign.getScheduleById 【getCubeCalendarInfo】 : reqVo: {}, result:{}", reqVo, result);
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("获取日程详情报错, reqVo: %s result: %s", reqVo, result), null);
            return null;
        }
        CubeCalendarInfoRespVo respVo = new CubeCalendarInfoRespVo();
        BeanUtil.copyProperties(result.getData(), respVo);
        respVo.setRepeatDays(respVo.getRepeating() == 1 ? respVo.getRepeatDays() : "");
        return respVo;
    }

    public CubeRepeatCheckCalendarRespVo modifyCubeCalendarStatus(CubeCalendarStatusReqVo reqVo) {
        SetScheduleEnabledRequest request = initCalendarStatusRequest(reqVo);
        JsonResult<UserSchedule> result = cubeControlServerFeign.setScheduleEnabled(request);
        log.info("cubeControlServerFeign.setScheduleEnabled 【modifyCubeCalendarStatus】 : reqVo: {}, result:{}", request, result);
        if (result != null && result.getCode() == CUBE_REPEAT_ERROR_CODE) {
            log.error(String.format("修改cube日程状态失败-时间重复, reqVo: %s result: %s", request, result));
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_ALARM_TIME_CONFLICT).build();
        }
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val()) {
            alarmXtqService.alarmAndLogForError(String.format("修改cube状态异常, reqVo: %s result: %s", request, result), null);
            return CubeRepeatCheckCalendarRespVo.builder().resultEnum(CUBE_ALARM_DEFAULT_ERROR).build();
        }
        return CubeRepeatCheckCalendarRespVo.builder().resultEnum(SUCCESS).modifyResult(result.getData() != null ? true : false).build();
    }

    private SetScheduleEnabledRequest initCalendarStatusRequest(CubeCalendarStatusReqVo reqVo) {
        SetScheduleEnabledRequest request = new SetScheduleEnabledRequest();
        BeanUtil.copyProperties(reqVo, request);
        request.setRepeating(StrUtil.isNotBlank(reqVo.getRepeatDays()) ? 1 : 0);
//        if (request.getRepeating() == 0) {
//            //根据闹钟秒数和当前时间的秒数比较，大于则表示日期是明天，小于则表示日期是今天，获取具体的时间字符串 yyyymmdd
//            request.setScheduleDay(calculateAlarmDate(reqVo.getScheduleTime()));
//        }
        request.setLastModifiedBy(CubeSourceEnum.PARENT.val());
        return request;
    }

    public CubeNightLightInfoRespVo getCubeNightLightInfo(CubeNightLightInfoReqVo reqVo) {
        JsonResult<NightLightConfig> result = cubeControlServerFeign.getCubeNightLightInfo(reqVo);
        log.info("cubeControlServerFeign.getCubeNightLightInfo 【getCubeNightLightInfo】 : reqVo: {}, result:{}", reqVo, result);
        if (result == null || result.getCode() != ResultEnum.SUCCESS.val() || result.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("获取cube夜灯信息报错, reqVo: %s result: %s", reqVo, result), null);
            return null;
        }
        CubeNightLightInfoRespVo respVo = new CubeNightLightInfoRespVo();
        BeanUtil.copyProperties(result.getData(), respVo);
        return respVo;
    }

    public NightLightConfig modifyCubeNightLightInfo(CubeNightLightInfoModifyReqVo reqVo) {
        JsonResult<NightLightConfig> result = cubeControlServerFeign.modifyCubeNightLightInfo(reqVo);
        log.info("cubeControlServerFeign.modifyCubeNightLightInfo 【modifyCubeNightLightInfo】 : reqVo: {}, result:{}", reqVo, result);
        if (result == null) {
            alarmXtqService.alarmAndLogForError(String.format("编辑cube夜灯信息报错, reqVo: %s result: %s", reqVo, result), null);
            return null;
        }
        return result.getData();
    }


    public Integer getCubeLoginStatus(CubeBindAndLoginReqVo reqVo) {
        //2 轮询调用周帆绑定接口
        JsonResult<DeviceLoginStatusResponse> jsonResult = userCenterFeign.cubeLogin(reqVo);
        log.info("getCubeLoginStatus req:{} resp:{}", reqVo, jsonResult);
        if (jsonResult != null && jsonResult.getCode() == ResultEnum.SUCCESS.val()
                && jsonResult.getData() != null) {
            return jsonResult.getData().getStatus();
        }
        return null;
    }

    /**
     * 判断设备是否归属
     *
     * @param sn
     * @param childTalId
     * @param os
     * @return
     */
    public boolean checkCubeSnState(String sn, String childTalId, String os) {
        //1 根据孩子id查询familyId
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(childTalId, os);
        log.info("checkCubeSnState queryUserInfo req:{} resp:{}", childTalId, userInfoData);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            return false;
        }
        String childFamilyId = userInfoData.getData().getFamily_id();
        //2 根据familyId查询设备、孩子绑定和登录信息，必须确保孩子和设备要有登录关系
        ServiceResult<TalFamilyUserAndSnInfo> familyUserSnInfo = talUserService.queryChildAndSnListByFamilyId(childFamilyId, os);
        log.info("checkCubeSnState queryChildAndSnListByFamilyId req:{} resp:{}", childFamilyId, familyUserSnInfo);
        if (!familyUserSnInfo.isSuccess() || familyUserSnInfo.getData() == null || CollectionUtil.isEmpty(familyUserSnInfo.getData().getHome_list())) {
            return false;
        }
        Boolean checkState = false;
        for (TalFamilyUserAndSnSubInfo info : familyUserSnInfo.getData().getHome_list()) {
            if (info.getDevice_sn().equals(sn) && info.getStu_talid().equals(childTalId) && info.getLogin_status() != null && info.getLogin_status() == 1) {
                checkState = true;
                break;
            }
        }
        return checkState;
    }

    public void cubeWakeUp(CubeWakeUpReqVo reqVo) {
        talCubeService.cubeWakeUp(reqVo);
    }
}
