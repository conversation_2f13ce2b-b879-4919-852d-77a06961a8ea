package com.tal.sea.seaover.application.service.common;   // 解决package
// 默认导入lombok，方便日志打印


import cn.hutool.core.util.RandomUtil;
import com.google.gson.reflect.TypeToken;
import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.dto.cube.CubeWakeUpReqVo;
import com.tal.sea.seaover.application.dto.cube.IotDirectMethodReq;
import com.tal.sea.seaover.application.dto.family.FamilyMemberInfo;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.util.GsonUtil;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import com.tal.sea.xpod.tools.util.OkHttpHelperUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class TalCubeService {

    @Value("${cube.url}")
    private String baseUserPath;

    @Autowired
    private AlarmXtqService alarmXtqService;

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private final OkHttpClient client = OkHttpHelperUtil.createClient(10);

    /**
     * 【用户中心】退出登录
     */
    public void logout(String talId, String sn) {
        //调用IOT服务
        IotDirectMethodReq iotDirectMethodReq = new IotDirectMethodReq(sn, "logout");
        iotDirectMethodReq.setPayload("{\"talId\":\"" + talId + "\"}");
        iotDirectMethod(iotDirectMethodReq);
    }

    /**
     * 唤醒闹钟
     *
     * @param reqVo
     */
    public void cubeWakeUp(CubeWakeUpReqVo reqVo) {
        IotDirectMethodReq iotDirectMethodReq = new IotDirectMethodReq(reqVo.getSn(), "wakeUp");
        iotDirectMethodReq.setWaitResult(false);
        iotDirectMethodReq.setRetryCount(0);
        iotDirectMethod(iotDirectMethodReq);
    }

    public void iotDirectMethod(IotDirectMethodReq iotDirectMethodReq) {
        String traceId = ThreadMdcUtil.getTraceId();
        String requestParameter = GsonUtil.toJson(iotDirectMethodReq);
        log.info("调用Iot服务 traceId:{} 参数:{}", traceId, requestParameter);
        if (StringUtils.isEmpty(requestParameter)) {
            log.error("调用Iot服务参数为空 traceId:{}", traceId);
            return;
        }
        try {
            RequestBody requestBody = RequestBody.create(JSON, requestParameter);
            Request request = new Request.Builder()
                    .url(baseUserPath)
                    .post(requestBody)
                    .addHeader("traceId", traceId)
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("调用Iot服务异常 traceId:{}, request:{}, response:{}", traceId, requestParameter, response);
                alarmXtqService.alarm("调用Iot服务 failed, traceId: " + traceId + " || request: " + requestParameter +
                        " || response.code: " + response.code() + " || response.msg: " + response.message());
                return;
            }
            String body = response.body().string();
            log.info("调用Iot服务结果Info traceId:{} request:{} response: {}", traceId, requestParameter, body);
        } catch (Exception e) {
            log.error("调用Iot服务异常 , request:{},  e:{}", requestParameter, e.getMessage(), e);
            alarmXtqService.alarm("调用Iot服务异常, request:" + requestParameter + " || e.message: " + e.getMessage());
        }
    }


}