package com.tal.sea.seaover.application.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.*;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.sn.DeviceLocalInfo;
import com.tal.sea.seaover.application.dto.sn.FamilySnChildInfo;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.dto.user.AccountDeleteReqVo;
import com.tal.sea.seaover.application.dto.user.FamilyLogOutRequestVo;
import com.tal.sea.seaover.application.dto.user.SnInfo;
import com.tal.sea.seaover.application.enums.DeviceTypeEnum;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.service.common.*;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.service.user.AccountDeleteAsyncService;
import com.tal.sea.seaover.application.service.user.AccountDeleteService;
import com.tal.sea.seaover.application.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.constant.RedisConstant.SN_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AccountDeleteAsyncServiceImpl implements AccountDeleteAsyncService {

    @Autowired
    private TalUserService talUserService;
    @Autowired
    private UserCenterFeign userCenterFeign;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TalSmsService talSmsService;
    @Autowired
    private AlarmXtqService alarmXtqService;
    @Autowired
    private DeviceFeign deviceFeign;
    @Autowired
    private TalCubeService talCubeService;


    @Override
    @Async("asyncExecutor")
    public void asyncLogoutDevice(String familyId, String talId, String os) {
        //1 获取待删除列表
        List<FamilyChildInfoV2> childInfoList = getNeedLogoutDeviceMap(familyId, os);

        //2 依次根据设备类型登出设备
        if (CollectionUtil.isEmpty(childInfoList)) {
            return;
        }
        for (FamilyChildInfoV2 childInfo : childInfoList) {
            if (CollectionUtil.isEmpty(childInfo.getSnList())) {
                continue;
            }
            for (FamilyChildInfoBody device : childInfo.getSnList()) {
                if (DeviceTypeEnum.PAWPAL.getValue() == device.getDeviceType()) {
                    //闹钟注销退出
                    deleteDevicePawPal(childInfo.getTalId(), device);
                } else {
                    //学习机注销退出
                    deleteDeviceTalPad(childInfo.getTalId(), device);
                }
            }
        }
    }

    private void deleteDeviceTalPad(String childTalId, FamilyChildInfoBody device) {
        //1 发送push消息
        // 通知消息
        talSmsService.sendMessageWithRetry(Collections.singletonList(childTalId), PushParam.SmsTypeEnum.NOTICE.getType(), TalParentServerProperties.SmsBizEnum.CHILD_DEVICE_DELETE.getName());
        // 静默消息
        talSmsService.sendMessageWithRetry(Collections.singletonList(childTalId), PushParam.SmsTypeEnum.SILENT.getType(), TalParentServerProperties.SmsBizEnum.CHILD_LOGOUT.getName());
        //2 同步616
        FamilyLogOutRequestVo requestVo = FamilyLogOutRequestVo.builder().talId(childTalId).deviceSn(device.getSn()).deviceType(device.getDeviceType()).build();
        JsonResult jsonResult = userCenterFeign.familyDeleteLogout(requestVo);
        log.info("userCenterFeignInfo 【familyDeleteLogout】 : reqVo: {}, result:{}", requestVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            log.error("家长端删除账号登出上报失败, reqVo: {},  result: {}", requestVo, jsonResult);
            alarmXtqService.alarm(String.format("家长端将孩子退出登录上报失败, reqVo: %s,  result: %s", requestVo, jsonResult));
        }
    }

    private void deleteDevicePawPal(String childTalId, FamilyChildInfoBody device) {
        //1 同步616
        FamilyLogOutRequestVo requestVo = FamilyLogOutRequestVo.builder().talId(childTalId).deviceSn(device.getSn()).deviceType(device.getDeviceType()).build();
        JsonResult jsonResult = userCenterFeign.familyDeleteLogout(requestVo);
        log.info("userCenterFeignInfo 【familyDeleteLogout】 : reqVo: {}, result:{}", requestVo, jsonResult);
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()) {
            log.error("家长端删除账号登出上报失败, reqVo: {},  result: {}", requestVo, jsonResult);
            alarmXtqService.alarm(String.format("家长端将孩子退出登录上报失败, reqVo: %s,  result: %s", requestVo, jsonResult));
        }
        //2 调用设备退出接口
        talCubeService.logout(childTalId, device.getSn());
    }

    private List<FamilyChildInfoV2> getNeedLogoutDeviceMap(String familyId, String os) {
        ServiceResult<TalFamilyUserAndSnInfo> familyUserSnInfo = talUserService.queryChildAndSnListByFamilyId(familyId, os);
        log.info("getNeedLogoutDeviceMap 【queryChildAndSnListByFamilyId】 : familyId: {}, os: {}, result:{}", familyId, os, familyUserSnInfo);
        if (!familyUserSnInfo.isSuccess()) {
            alarmXtqService.alarmAndLogForError(String.format("家长端删除账号-查询家庭成员信息失败, familyId: %s  result: %s", familyId, familyUserSnInfo), null);
            return Collections.emptyList();
        }
        if (familyUserSnInfo.getData() == null ||
                CollectionUtil.isEmpty(familyUserSnInfo.getData().getHome_list())) {
            return Collections.emptyList();
        }

        Map<String, FamilyChildInfoV2> childMap = new HashMap<>();
        for (TalFamilyUserAndSnSubInfo l : familyUserSnInfo.getData().getHome_list()) {
            String deviceSn = validateDeviceSn(l, familyId);
            if (StrUtil.isBlank(deviceSn)) {
                continue;
            }
            FamilyChildInfoV2 childInfo = childMap.computeIfAbsent(l.getStu_talid(), k ->
                    FamilyChildInfoV2.builder()
                            .talId(l.getStu_talid())
                            .nickname(l.getNickname())
                            .avatarUrl(l.getAvatar_url())
                            .snList(new ArrayList<>())
                            .build()
            );
            childInfo.getSnList().add(FamilyChildInfoBody.builder().sn(deviceSn).build());
        }
        List<FamilyChildInfoV2> childInfos = new ArrayList<>(childMap.values());
        if (CollectionUtil.isEmpty(childInfos)) {
            return Collections.emptyList();
        }
        initAllDevices(childInfos);
        return deduplicateByDeviceTypeAndSnModel(childInfos);
    }

    private String validateDeviceSn(TalFamilyUserAndSnSubInfo l, String familyId) {
        if (StrUtil.isBlank(l.getDevice_sn()) || l.getLogin_status() == null || l.getLogin_status() != 1) {
            return null;
        }
        //二次判断设备是否游离
        ServiceResult<TalDeviceSnBindInfo> bindInfo = talUserService.queryDeviceBySn(l.getDevice_sn());
        log.info("getNeedLogoutDeviceMap 【queryDeviceBySn】 : sn: {}, result:{}", l.getDevice_sn(), bindInfo);
        if (bindInfo.isSuccess() && bindInfo.getData() != null &&
                familyId.equals(bindInfo.getData().getFamily_id())) {
            return l.getDevice_sn();
        }
        return null;
    }

    private void initAllDevices(List<FamilyChildInfoV2> childInfos) {
        childInfos.stream()
                .filter(child -> CollectionUtil.isNotEmpty(child.getSnList()))
                .flatMap(child -> child.getSnList().stream())
                .forEach(this::initDeviceInfo2);
    }

    private void initDeviceInfo2(FamilyChildInfoBody device) {
        String redisVal = redisService.getString(SN_INFO_KEY + device.getSn());
        if (StrUtil.isNotBlank(redisVal)) {
            SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
            device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
            device.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
            return;
        }
        JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(device.getSn());
        log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", device.getSn(), snInfoJsonResult);
        if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
            SnInfo snInfo = snInfoJsonResult.getData();
            device.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            device.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            device.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getPictureUrl() : "");
            device.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
            redisService.setString(SN_INFO_KEY + device.getSn(), GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
        }
    }

    private List<FamilyChildInfoV2> deduplicateByDeviceTypeAndSnModel(List<FamilyChildInfoV2> childInfos) {
        return childInfos.stream()
                .map(childInfo -> {
                    List<FamilyChildInfoBody> dedupedList = childInfo.getSnList().stream()
                            .collect(Collectors.toMap(
                                    item -> item.getDeviceType() + "-" + item.getSnModel(),
                                    Function.identity(),
                                    (oldVal, newVal) -> newVal,
                                    LinkedHashMap::new          // 保持插入顺序
                            ))
                            .values()
                            .stream()
                            .collect(Collectors.toList());
                    childInfo.setSnList(dedupedList);
                    return childInfo;
                }).collect(Collectors.toList());
    }
}
