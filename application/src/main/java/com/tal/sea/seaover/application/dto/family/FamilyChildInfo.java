package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyChildInfo {

    @Schema(description = "用户ID", example = "user123")
    private String talId;

    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户昵称", example = "小张")
    private String nickname;

    @Schema(description = "登录的设备sn号", example = "12345667")
    private String sn;

    @Schema(description = "登录的设备型号", example = "T100")
    private String snModel;

    @Schema(description = "登录的设备名称", example = "Thinkpal Tablet T100")
    private String snName;

    @Schema(description = "登录的设备sn图片", example = "http://111111.jpg")
    private String snImg;
}