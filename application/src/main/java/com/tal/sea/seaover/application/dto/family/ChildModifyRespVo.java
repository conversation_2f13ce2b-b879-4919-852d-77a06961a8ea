package com.tal.sea.seaover.application.dto.family;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChildModifyRespVo {
    /**
     * 孩子TalId
     */
    @NotEmpty(message = "孩子TalId  cannot be empty")
    private String talId;
    /**
     * 用户头像
     */
    @NotEmpty(message = "用户头像  cannot be empty")
    private String avatarUrl;
    /**
     * 用户昵称
     */
    @NotEmpty(message = "用户昵称  cannot be empty")
    private String nickname;
    /**
     * 用户年级
     */
    @NotEmpty(message = "用户年级  cannot be empty")
    private Integer grade;
    /**
     * 用户性别
     */
    @NotEmpty(message = "用户性别  cannot be empty")
    private Integer sex;


}
