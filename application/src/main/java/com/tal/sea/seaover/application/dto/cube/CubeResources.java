package com.tal.sea.seaover.application.dto.cube;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class CubeResources {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 适用于的业务 1闹钟 2日程
     */
    private Integer busType;
    /**
     * 资源名称
     */
    private String name;

    /**
     * 资源类型 1.icon 2.ring
     */
    private Integer type;

    /**
     * 文件唯一标识(设备端和家长端保持一致)
     */
    private String fileId;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;
}
