package com.tal.sea.seaover.application.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {
    @Schema(description = "用户token", example = "abc123")
    private String talToken;

    @Schema(description = "用户ID", example = "user123")
    private String talId;

    @Schema(description = "用户名", example = "张三")
    private String talName;

    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户昵称", example = "小张")
    private String nickname;

    @Schema(description = "性别", example = "性别 1男 2女")
    private Integer sex;

    @Schema(description = "在家庭中的身份", example = "在家庭中的身份：0-未知 1-妈妈 2-爸爸 3-爷爷 4-奶奶 5-外公 6-外婆 7-祖父 8-祖母 98-家人 99-孩子")
    private Integer familyRole;

    @Schema(description = "秘钥截取开始位置", example = "1")
    private int secretStart;
    @Schema(description = "秘钥截取结束位置", example = "5")
    private int secretEnd;
    /**
     * 账号状态（1：正常 2: 待删除）
     */
    private Integer status;
}