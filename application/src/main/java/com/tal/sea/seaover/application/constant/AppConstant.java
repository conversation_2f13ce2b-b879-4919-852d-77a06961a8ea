package com.tal.sea.seaover.application.constant;

public class AppConstant {
    public final static String COST = "cost";
    public final static String LOG_SPAN_ID = "spanId";
    public final static String LOG_TRACE_ID = "traceId";
    public static final String HEADER_TAL_ID = "talId";
    public static final String HEADER_RPC_ID = "rpcId";
    public static final String HEADER_SN = "X-Tal-Sn";
    public static final String HEADER_TAL_ID_TYPE = "talIdType";
    public static final String HEADER_APP_VERSION = "X-Tal-App-VersionName";
    public static final String HEADER_APP_PKG_NAME = "X-Tal-PackageName";


    public static final String LOGIN_SECRET = "Ae5FgH9iJkLmN3oPqRtS";


    //身份token
    public static final String AUTHORIZATION = "Authorization";

    //时间戳
    public static final String X_TAL_TIMESTAMP = "X-Tal-Timestamp";

    //随机数
    public static final String X_TAL_NONCE = "X-Tal-Nonce";

    //签名 按照验签规则生成的 md5 值
    public static final String X_TAL_SIGN = "X-Tal-Sign";

    //app版本号
    public static final String X_TAL_APP_VERSION = "X-Tal-App-Version";

    //app设备类型 android / ios
    public static final String X_TAL_OS = "X-Tal-OS";

    //位置信息
    public static final String X_TAL_GPS = "X-Tal-Gps";
    //时区信息
    public static final String X_TAL_TIME_ZONE = "X-Tal-Time-Zone";

    //日志追踪ID 端侧生成的调用链路id
    public static final String X_TAL_TRACEID = "X-Tal-TraceId";

    //系统，Android
    public static final String ANDROID = "android";


    /**
     * 绑定分布式状态码
     */
    public static final String X_TAL_BIND_INIT_KEY = "0";
    public static final String X_TAL_BIND_SUCCESS_KEY = "1";
    public static final String X_TAL_BIND_ERROR_KEY = "2";


    //退出登录默认参数
    public static Integer DEFAULT_CG = 94;

    //用户中心 未查到相关信息
    public static final Integer USER_NOT_FOUND = 20012;
}
