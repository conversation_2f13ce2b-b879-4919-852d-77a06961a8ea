package com.tal.sea.seaover.application.dto.taluser;   // 解决package

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@Builder
public class UserInfoRequest {
    private String tal_id;

    private String client_id;

    private String token;

    private String device_sn;

    private String family_id;
    private Integer role_type;
    private String parent_tal_id;
    private String child_tal_id;
    private Integer family_role;
    private Integer cg;//用户退出登录上报时默认传94

    private String nickname;

    private String tal_name;
    private Integer grade;
    private Integer sex;
    /**
     * 头像url
     */
    private String avator_url;
}