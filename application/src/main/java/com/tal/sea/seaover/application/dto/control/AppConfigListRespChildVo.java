package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AppConfigListRespChildVo {
    @Schema(description = "pkgName", example = "xxx")
    private String pkgName;
    @Schema(description = "appName", example = "xxx")
    private String appName;
    @Schema(description = "appImgUrl", example = "xxx")
    private String appImgUrl;
    @Schema(description = "suiteList", example = "xxx")
    private List<AppConfigListRespSubChildVo> suiteList;
}
