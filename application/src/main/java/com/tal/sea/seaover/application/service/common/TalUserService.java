package com.tal.sea.seaover.application.service.common;   // 解决package
// 默认导入lombok，方便日志打印


import cn.hutool.core.util.RandomUtil;
import com.google.gson.reflect.TypeToken;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.dto.family.FamilyMemberInfo;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.enums.RoleTypeEnum;
import com.tal.sea.seaover.application.util.GsonUtil;
import com.tal.sea.xpod.tools.util.OkHttpHelperUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;

@Slf4j
@Service
public class TalUserService {

    @Value("${udc.url.base}")
    private String baseUserPath;
    @Value("${udc.clientId}")
    private String defaultClientId;
    @Value("${udc.android-clientId}")
    private String androidClientId;
    @Value("${udc.android-packageName}")
    private String androidOsPackageName;
    @Value("${udc.ios-clientId}")
    private String iosClientId;
    @Value("${udc.ios-packageName}")
    private String iosOsPackageName;
    @Autowired
    private TalParentServerProperties properties;

    @Autowired
    private AlarmXtqService alarmXtqService;

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private final OkHttpClient client = OkHttpHelperUtil.createClient(10);

    /**
     * 【用户中心】获取用户Tal信息
     */
    public ServiceResult<TalUserInfo> queryUserInfo(String talId, String os) {
        String url = baseUserPath + "/api/v1/user/info";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(getOsClientByOsName(os)).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalUserInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】获取用户Tal信息-包括家庭信息
     */
    public ServiceResult<TalUserInfoPlus> queryUserInfoPlus(String talId, String os) {
        String url = baseUserPath + "/api/v1/user/profile";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(getOsClientByOsName(os)).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalUserInfoPlus>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }


    /**
     * 【用户中心】修改用户信息
     */
    public ServiceResult modifyUserInfo(String talId, String nickname, String avatarUrl, Integer grade, String os) {
        String url = baseUserPath + "/api/v1/user/modify";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).nickname(nickname).avator_url(avatarUrl).grade(grade).client_id(getOsClientByOsName(os)).build();
        Type responseType = new TypeToken<TalUserCommonResponse>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】获取用户Tal信息-无os
     */
    public ServiceResult<TalUserInfo> queryUserInfoWithOutOs(String talId) {
        String url = baseUserPath + "/api/v1/user/info";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(defaultClientId).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalUserInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】获取用户google信息
     */
    public ServiceResult<TalGoogleUserInfo> queryUserGoogleInfo(String talId, String os) {
        String url = baseUserPath + "/api/v1/user/google/profile";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(getOsClientByOsName(os)).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalGoogleUserInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】退出登录
     */
    public ServiceResult logout(String talId, String os) {
        String url = baseUserPath + "/api/v1/user/logout";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(getOsClientByOsName(os)).cg(AppConstant.DEFAULT_CG).build();
        Type responseType = new TypeToken<TalUserCommonResponse>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】根据家庭Id查询家庭成员列表
     */
    public ServiceResult<List<TalFamilyUserInfo>> queryChildListByFamilyId(String familyId, String os) {
        String url = baseUserPath + "/api/v1/user/logout";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().family_id(familyId).build();
        Type responseType = new TypeToken<TalUserCommonResponse<List<TalFamilyUserInfo>>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】根据家庭Id查询家庭成员-设备列表[包括历史登录信息]
     */
    public ServiceResult<TalFamilyUserAndSnInfo> queryChildAndSnListByFamilyId(String familyId, String os) {
        String url = baseUserPath + "/api/v1/parent/child/device/list";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().family_id(familyId).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalFamilyUserAndSnInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】根据家庭Id查询设备列表[包括历史登录信息]
     */
    public ServiceResult<TalFamilySnInfo> querySnListByTalId(String parentTalId) {
        String url = baseUserPath + "/api/v1/device/list";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(parentTalId).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalFamilySnInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】查询设备信息
     */
    public ServiceResult<TalDeviceSnBindInfo> queryDeviceBySn(String sn) {
        String url = baseUserPath + "/api/v1/device/info";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().device_sn(sn).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalDeviceSnBindInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】根据家庭Id查询家庭成员列表
     */
    public ServiceResult<FamilyMemberInfo> queryFamilyMemberById(String familyId) {
        String url = baseUserPath + "/api/v1/family/member/list";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().family_id(familyId).cg(AppConstant.DEFAULT_CG).build();
        Type responseType = new TypeToken<TalUserCommonResponse<FamilyMemberInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】创建家庭&绑定设备
     */
    public ServiceResult<TalCreateFamilyAndBindSnInfo> createFamilyAndBindSn(String parentTalId, String childTalId, String sn) {
        String url = baseUserPath + "/api/v1/family/establish";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().parent_tal_id(parentTalId).client_id(defaultClientId).child_tal_id(childTalId).device_sn(sn).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalCreateFamilyAndBindSnInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】加入家庭&绑定设备
     */
    public ServiceResult<TalJoinFamilyAndBindSnInfo> joinFamilyAndBindSn(String childTalId, String sn, String familyId) {
        String url = baseUserPath + "/api/v1/family/member/join";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().family_id(familyId).client_id(defaultClientId).tal_id(childTalId).family_role(RoleTypeEnum.STUDENT.val()).device_sn(sn).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalJoinFamilyAndBindSnInfo>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】修改家庭角色
     */
    public ServiceResult updateFamilyRoleInfo(String talId, Integer roleId, String familyId) {
        String url = baseUserPath + "/api/v1/family/parent/role/set";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().role_type(roleId).family_id(familyId).build();
        Type responseType = new TypeToken<TalUserCommonResponse>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】获取用户孩子Tal信息-包括家庭信息
     */
    public ServiceResult<TalUserInfoPlus> queryChildUserInfoPlus(String talId) {
        String url = baseUserPath + "/api/v1/user/profile";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder().tal_id(talId).client_id(defaultClientId).build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalUserInfoPlus>>() {
        }.getType();
        return callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
    }

    /**
     * 【用户中心】创建账号
     */
    public ServiceResult<TalAddUserResult> createAccount(String nickname, Integer grade, Integer sex) {
        //由于可能用户名重试，默认重试3次
        String url = baseUserPath + "/api/v1/user/account/add";
        UserInfoRequest userInfoRequest = UserInfoRequest.builder()
                .tal_name(getRandomTalName())
                .client_id(defaultClientId)
                .nickname(nickname)
                .grade(grade)
                .sex(sex)
                .build();
        Type responseType = new TypeToken<TalUserCommonResponse<TalAddUserResult>>() {
        }.getType();

        int retryCount = 3;
        while (retryCount-- > 0) {
            ServiceResult<TalAddUserResult> result = callTalUserService(url, GsonUtil.toJson(userInfoRequest), responseType);
            if (result.isSuccess()) {
                return result;
            }
        }
        return new ServiceResult<>(null, false);
    }


    //8-12位
    private String getRandomTalName() {
        return "p" + RandomUtil.randomString(15);
    }

    private <T> ServiceResult<T> callTalUserService(String url, String reqJsonStr, Type responseType) {
        int maxRetries = properties.getPushMsgRetryMaxCount();
        int retryCount = 0;
        long initialDelay = 1000; // 初始延迟1秒
        long maxDelay = 5000;    // 最大延迟10秒
        while (retryCount < maxRetries) {
            try {
                RequestBody requestBody = RequestBody.create(JSON, reqJsonStr);
                Request request = new Request.Builder()
                        .url(url)
                        .post(requestBody)
                        .build();
                Response response = client.newCall(request).execute();
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("callUserServiceLog info url :{}  reqStr:{}  第{}次发送结果 result: {}", url, reqJsonStr, retryCount + 1, responseBody);

                    TalUserCommonResponse<T> commonResponse = GsonUtil.fromJsonByType(responseBody, responseType);
                    if (commonResponse != null && commonResponse.getErrcode() == 0) {
                        return new ServiceResult<>(commonResponse.getData(), true);
                    } else {
                        //  捕获异常码直接返回
                        if (commonResponse != null && (AppConstant.USER_NOT_FOUND.equals(commonResponse.getErrcode()))){
                            return null;
                        }
                        log.error("callUserServiceLog url :{}  error reqJsonStr: {} 第{}次发送结果 return:{}", url, reqJsonStr, retryCount + 1, responseBody);
                        alarmXtqService.alarm(String.format("调用用户中心结果返回失败,url: %s, reqJsonStr: %s, 第%s次发送结果, result: %s", url, reqJsonStr, retryCount + 1, responseBody));
                    }
                } else {
                    throw new RuntimeException("response is not success");
                }
            } catch (Exception e) {
                log.error("callUserServiceLog error url :{} reqJsonStr: {} 第{}次发送失败 return:{}", url, reqJsonStr, retryCount + 1, e.getMessage(), e);
                String errorMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
                alarmXtqService.alarm(String.format("调用用户中心失败,url: %s, reqJsonStr: %s, 第%s次发送失败, error: %s", url, reqJsonStr, retryCount + 1, errorMessage));
            }
            // 触发重试条件：结果为空或业务失败
            if (retryCount < maxRetries) {
                long delay = calculateExponentialBackoff(retryCount, initialDelay, maxDelay);
                log.info("callUserServiceLog 发送失败，准备第{}次重试，等待{}ms", retryCount + 1, delay);
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            //请求失败 递增计数器
            retryCount++;
        }
        log.error("调用用户中心重试耗尽，最终失败. url: {}, reqJsonStr: {}", url, reqJsonStr);
        alarmXtqService.alarm(String.format("调用用户中心重试耗尽，最终失败. url: %s, reqJsonStr: %s", url, reqJsonStr));
        return new ServiceResult<>(null, false);
    }

    private String getOsClientByOsName(String os) {
        return "ios".equals(os) ? iosClientId : androidClientId;
    }

    // 指数退避计算（含随机抖动避免惊群）
    private long calculateExponentialBackoff(int retryCount, long initialDelay, long maxDelay) {
        long delay = (long) (initialDelay * Math.pow(2, retryCount));
        delay = Math.min(delay, maxDelay);
        // 添加±20%随机抖动
        long jitter = (long) (delay * 0.2 * Math.random());
        return (long) (delay - jitter + Math.random() * 2 * jitter);
    }


}