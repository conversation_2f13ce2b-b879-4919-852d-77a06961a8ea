package com.tal.sea.seaover.application.dto.user;   // 解决package

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 登陆接口接口
 */
@Slf4j
@Data
@Builder
public class SnCheckResponse {
    private String deviceSn; //设备sn
    private Integer valid;  //是否正确，是否有效 0 无效   1 有效
    private Long createTime; //Long类型，创建时间，时间戳
    private Long updateTime;  //Long类型，更新时间，时间戳

}