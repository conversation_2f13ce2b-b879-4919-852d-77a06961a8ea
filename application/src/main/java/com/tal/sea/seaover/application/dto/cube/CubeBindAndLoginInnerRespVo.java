package com.tal.sea.seaover.application.dto.cube;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CubeBindAndLoginInnerRespVo {
    //绑定结果枚举
    ResultEnum resultEnum;

    String resultMsg;

    //绑定状态 0等待完成 1成功 2绑定失败
    Integer status;

    public enum CubeBindStatus {
        WAITING(0),
        SUCCESS(1),
        FAIL(2);

        private final int val;

        CubeBindStatus(int val) {
            this.val = val;
        }

        public int val() {
            return val;
        }
    }
}
