package com.tal.sea.seaover.application.dto.sms;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PushParam {
    @NotEmpty(message = "appId  cannot be empty")
    private String appId;
    @NotNull(message = "消息类型  cannot be empty")
    private Integer type; // 消息类型: 通知消息, 静默消息
    private Long sendTime; // 定时时间
    private String taskId;
    private String template; // 消息模板
    private List<String> userIds;
    @NotNull(message = "消息内容  cannot be empty")
    private PushData data; // 消息内容

    @Data
    public class deviceInfo {
        private String deviceId;
        private String userId;
        private String appId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PushData {
        private String title;
        private String body;
        private String topic;
        private String data;
    }

    public enum SmsTypeEnum {
        //1-通知消息  2-静默消息 3-广播通知消息 4-广播静默消息
        NOTICE(1), SILENT(2), BROADCAST_NOTICE(3), BROADCAST_SILENT(4);
        private int type;

        SmsTypeEnum(int type) {
            this.type = type;
        }

        public int getType() {
            return type;
        }
    }


}


