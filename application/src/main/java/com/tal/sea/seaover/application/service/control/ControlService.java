package com.tal.sea.seaover.application.service.control;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.control.*;
import com.tal.sea.seaover.application.dto.sms.PushParam;
import com.tal.sea.seaover.application.dto.talcontrol.*;
import com.tal.sea.seaover.application.dto.taluser.ServiceResult;
import com.tal.sea.seaover.application.dto.user.SnVersionInfo;
import com.tal.sea.seaover.application.service.common.AlarmXtqService;
import com.tal.sea.seaover.application.service.common.TalControlService;
import com.tal.sea.seaover.application.service.common.TalSmsService;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.config.result.ResultEnum.CUBE_ALARM_OFFLINE_ERROR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ControlService {

    @Autowired
    TalControlService talControlService;
    @Autowired
    TalSmsService talSmsService;
    @Autowired
    TalParentServerProperties properties;
    @Autowired
    DeviceFeign deviceFeign;
    @Autowired
    AlarmXtqService alarmXtqService;

    public UserFuncListRespVo userFuncList(UserFuncListReqVo reqVo) {
        UserFuncListRespVo userFunc = new UserFuncListRespVo();
        BeanUtil.copyProperties(reqVo, userFunc);
        ServiceResult<UserControl> userConfigInfo = talControlService.getUserConfigInfo(reqVo.getChildTalId(), reqVo.getSn());
        if (!userConfigInfo.isSuccess() || userConfigInfo.getData() == null || CollectionUtil.isEmpty(userConfigInfo.getData().getList())) {
            return userFunc;
        }
        UserControl data = userConfigInfo.getData();
        List<UserFuncListRespChildVo> transformedList = data.getList().stream()
                .map(ControlService::transformUserControlChild)
                .collect(Collectors.toList());
        //根据os版本进行拍搜拦截
        Boolean isSupport = getPaiSouOsVersionSupport(reqVo.getSn());
        if (!isSupport) {
            transformedList.removeIf(child -> child.getSuiteKey().equals(properties.getPaiSouControlSuiteKey()));
        }
        userFunc.setList(transformedList);
        return userFunc;
    }

    private Boolean getPaiSouOsVersionSupport(String sn) {
        JsonResult<SnVersionInfo> jsonResult = deviceFeign.queryDeviceVersion(sn);
        log.info("查询sn版本信息 deviceSn:{} jsonResult: {}", sn, jsonResult);
        Boolean isSupport = false;
        if (jsonResult == null || jsonResult.getCode() != ResultEnum.SUCCESS.val()
                || jsonResult.getData() == null || StrUtil.isBlank(jsonResult.getData().getSysVersion())) {
            alarmXtqService.alarmAndLogForError(String.format("查询设备版本信息失败, reqVo: %s result: %s", sn, jsonResult), null);
        } else {
            isSupport = snOsVersionCompare(jsonResult.getData().getSysVersion(), properties.getPaiSouMinOsVersion()) >= 0;
        }
        return isSupport;
    }

    private Integer snOsVersionCompare(String sysVersion, String paiSouMinOsVersion) {
        if (StrUtil.isBlank(sysVersion) || StrUtil.isBlank(paiSouMinOsVersion)) {
            return -1;
        }
        // 1. 提取后6位并转为整数
        int date1 = Integer.parseInt(sysVersion.substring(1));
        int date2 = Integer.parseInt(paiSouMinOsVersion.substring(1));

        // 2. 直接比较整数
        return Integer.compare(date1, date2);
    }

    public AppConfigListRespVo appConfigList(AppConfigListReqVo reqVo) {
        AppConfigListRespVo appConfig = new AppConfigListRespVo();
        BeanUtil.copyProperties(reqVo, appConfig);
        //1 获取应用列表
        ServiceResult<AppBeanControl> appListBean = talControlService.getAppList(reqVo.getSn());
        if (!appListBean.isSuccess() || appListBean.getData() == null || CollectionUtil.isEmpty(appListBean.getData().getApp_list())) {
            return appConfig;
        }
        List<String> pkgNames = appListBean.getData().getApp_list().stream().map(AppSubBeanControl::getPkg_name).collect(Collectors.toList());
        Map<String, AppSubBeanControl> pkgMap = appListBean.getData().getApp_list().stream().collect(Collectors.toMap(AppSubBeanControl::getPkg_name, l -> l, (existing, replacement) -> existing));

        //2 批量获取对应列表的管控数据
        ServiceResult<AppControl> appConfigInfo = talControlService.getAppConfigInfo(reqVo.getSn(), pkgNames);
        if (!appConfigInfo.isSuccess() || appConfigInfo.getData() == null || CollectionUtil.isEmpty(appConfigInfo.getData().getList())) {
            return appConfig;
        }

        AppControl data = appConfigInfo.getData();
        List<AppConfigListRespChildVo> transformedList = data.getList().stream()
                .map(child -> transformAppControlChild(child, pkgMap.get(child.getPkg_name())))
                .collect(Collectors.toList());
        appConfig.setList(transformedList);
        return appConfig;
    }

    public Boolean userConfigSet(UserConfigSetReqVo reqVo) {
        //拍搜特殊点-用户维度
        if (StrUtil.isNotBlank(reqVo.getSuiteKey()) && reqVo.getSuiteKey().equals(properties.getPaiSouControlSuiteKey())) {
            reqVo.setSn(null);
        }
        ServiceResult serviceResult = talControlService.userConfigSet(reqVo);
        if (!serviceResult.isSuccess()) {
            return false;
        }
        //推送消息
        TalParentServerProperties.SmsPushData pushData = properties.getPushMessages().get(TalParentServerProperties.SmsBizEnum.PARENT_USER_CONTROL_PUSH.getName());
        String body = pushData.getData().replace("%s", reqVo.getUnitInfo().get(0).getUnitKey());
        Boolean sendState2 = talSmsService.sendMessage(Collections.singletonList(reqVo.getChildTalId()), PushParam.SmsTypeEnum.SILENT.getType(), TalParentServerProperties.SmsBizEnum.PARENT_USER_CONTROL_PUSH.getName(), body);
        if (!sendState2) {
            return false;
        }
        return true;
    }

    public Boolean appConfigSet(AppConfigSetReqVo reqVo) {
        ServiceResult serviceResult = talControlService.appConfigSet(reqVo);
        if (!serviceResult.isSuccess()) {
            return false;
        }
        //推送消息
        Boolean sendState2 = talSmsService.sendMessage(Collections.singletonList(reqVo.getChildTalId()), PushParam.SmsTypeEnum.SILENT.getType(), TalParentServerProperties.SmsBizEnum.PARENT_SN_CONTROL_PUSH.getName());
        if (!sendState2) {
            return false;
        }
        return true;
    }


    private static UserFuncListRespChildVo transformUserControlChild(UserControlChild child) {
        UserFuncListRespChildVo respChildVo = new UserFuncListRespChildVo();
        respChildVo.setSuiteKey(child.getSuite_key());
        respChildVo.setSuiteName(child.getSuite_name());
        respChildVo.setSuiteDesc(child.getSuite_desc());

        if (child.getUnits() != null) {
            List<ControlRespChildUnitVo> unitList = transformUserControlSubChild(child.getUnits(), 0L);
            respChildVo.setUnitList(unitList);
        }
        return respChildVo;
    }

    private static AppConfigListRespChildVo transformAppControlChild(AppControlChild child, AppSubBeanControl beanControl) {
        AppConfigListRespChildVo respChildVo = new AppConfigListRespChildVo();
        respChildVo.setPkgName(child.getPkg_name());
        respChildVo.setAppName(beanControl.getApp_name());
        respChildVo.setAppImgUrl(beanControl.getApp_img_url());

        if (CollectionUtil.isNotEmpty(child.getSuites())) {
            List<AppConfigListRespSubChildVo> suiteList = child.getSuites().stream()
                    .map(ControlService::transformAppControlSubChild)
                    .collect(Collectors.toList());
            respChildVo.setSuiteList(suiteList);
        }

        return respChildVo;
    }

    private static AppConfigListRespSubChildVo transformAppControlSubChild(AppControlSubChild appControlSubChild) {
        AppConfigListRespSubChildVo respSubChildVo = new AppConfigListRespSubChildVo();
        respSubChildVo.setSuiteKey(appControlSubChild.getSuite_key());
        respSubChildVo.setSuiteName(appControlSubChild.getSuite_name());
        respSubChildVo.setSuiteDesc(appControlSubChild.getSuite_desc());

        if (appControlSubChild.getUnits() != null) {
            List<ControlRespChildUnitVo> unitList = transformUserControlSubChild(appControlSubChild.getUnits(), 0L);
            respSubChildVo.setUnit_list(unitList);
        }
        return respSubChildVo;
    }

    private static List<ControlRespChildUnitVo> transformUserControlSubChild(List<UserControlSubChild> subChildren, Long parentId) {
        List<ControlRespChildUnitVo> result = new ArrayList<>();
        for (UserControlSubChild subChild : subChildren) {
            if (subChild.getPid().equals(parentId)) {
                ControlRespChildUnitVo controlRespChildUnitVo = new ControlRespChildUnitVo();
                controlRespChildUnitVo.setUnitKey(subChild.getUnit_key());
                controlRespChildUnitVo.setUnitValue(subChild.getUnit_value());
                controlRespChildUnitVo.setUnitName(subChild.getUnit_name());
                controlRespChildUnitVo.setUnitDesc(subChild.getUnit_desc());
                controlRespChildUnitVo.setId(subChild.getId());
                controlRespChildUnitVo.setPid(subChild.getPid());
                controlRespChildUnitVo.setType(subChild.getType());
                controlRespChildUnitVo.setChild(transformUserControlSubChild(subChildren, subChild.getId()));
                result.add(controlRespChildUnitVo);
            }
        }
        return result;
    }

}
