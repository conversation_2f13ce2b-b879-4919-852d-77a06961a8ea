package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CubeAlarmListChildRespVo {
    /**
     * 主键 ID
     */
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一 UUID
     */
    private String unionId;

    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（24 小时制 HH:MM 转为秒级时间）
     */
    private Long alarmTime;

    /**
     * 重复日（1 - 7，周一到周日）
     */
    private String repeatDays;

    /**
     * 是否重复，0 - 否，1 - 是
     */
    private Integer repeating;

    /**
     * 展示重复文案
     */
    private String repeatDaysDescription;


    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;
    /**
     * icon URL
     */
    private String iconUrl;
    /**
     * 铃声 ID
     */
    private String ringId;
    /**
     * 铃声 URL
     */
    private String ringUrl;

    /**
     * 是否启用，0 - 否，1 - 是
     */
    private Integer enabled;

    /**
     * 是否预置闹钟 0 - 否，1 - 是
     */
    private Integer isPreSet;
}
