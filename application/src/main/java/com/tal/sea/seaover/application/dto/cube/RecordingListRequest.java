package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询用户留言请求
 */
@Data
public class RecordingListRequest implements Serializable {

    /**
     * 用户ID
     */
    @NotBlank(message = "talId cannot be blank")
    private String talId;

    /**
     * 页大小
     */
    @NotNull(message = "pageSize cannot be null")
    @Min(value = 1, message = "pageSize must be greater than 0")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "page cannot be null")
    @Min(value = 1, message = "page must be greater than 0")
    private Integer page;
}
