package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;

@Data
public class QrCodeCheckRespVo {
    /**
     * 临时token
     */
    private String tempToken;
    /**
     * 有效期
     */
    private Long expiresIn;
    /**
     * 过期时间 时间戳格式
     */
    private Long expireTime;
    /**
     * 设备sn码
     */
    private String deviceSn;
}
