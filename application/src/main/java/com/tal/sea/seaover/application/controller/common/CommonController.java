package com.tal.sea.seaover.application.controller.common;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.common.SecretGetReqVo;
import com.tal.sea.seaover.application.service.common.CommonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;

/**
 * 公共配置等接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/common")
@Tag(name = "公共接口", description = "与业务无关，鉴权等接口")
public class CommonController {

    @Autowired
    private CommonService commonService;
    
    @PostMapping("/sec/get")
    @Operation(summary = "秘钥获取接口", description = "用户网关鉴权时秘钥获取")
    public JsonResult<String> getSecret(@Validated @RequestBody SecretGetReqVo reqVo) {
        if (StrUtil.isBlank(reqVo.getTalId())) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        return JsonResult.buildSuccessResult(commonService.getSecret(reqVo.getTalId()));
    }

}
