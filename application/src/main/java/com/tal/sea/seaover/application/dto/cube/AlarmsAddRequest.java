package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class AlarmsAddRequest {
    /**
     * tal_id
     */
    @NotBlank(message = "talId  cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;

    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId  cannot be empty")
    private String unionId;

    /**
     * 闹钟名称
     */
    @NotBlank(message = "alarmName  cannot be empty")
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    @NotNull(message = "闹钟时间  cannot be empty")
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "闹钟是否重复  cannot be empty")
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId  cannot be empty")
    private String iconId;

    /**
     * 铃声ID
     */
    @NotBlank(message = "ringId  cannot be empty")
    private String ringId;

    /**
     * 是否启用，0-否，1-是
     */
    @NotNull(message = "是否启用  cannot be empty")
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    @NotNull(message = "是否预置闹钟  cannot be empty")
    private Integer isPreSet;

    /**
     * 最后修改者，1-device，2-parent
     */
    @NotNull(message = "最后修改者  cannot be empty")
    private Integer lastModifiedBy;

}
