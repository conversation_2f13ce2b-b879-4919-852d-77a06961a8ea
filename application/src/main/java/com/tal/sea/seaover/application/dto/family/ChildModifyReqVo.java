package com.tal.sea.seaover.application.dto.family;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChildModifyReqVo {
    /**
     * 孩子TalId
     */
    @NotEmpty(message = "talId  cannot be empty")
    private String talId;
    /**
     * 用户头像
     */
    @NotEmpty(message = "avatarUrl  cannot be empty")
    private String avatarUrl;
    /**
     * 用户昵称
     */
    @NotEmpty(message = "nickname  cannot be empty")
    private String nickname;
    /**
     * 用户年级
     */
    @NotNull(message = "grade  cannot be empty")
    private Integer grade;


}
