package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

@Data
public class IotDirectMethodReq {
    /**
     * 设备SN
     */
    private String deviceSn;
    /**
     * 设备上的方法
     */
    private String methodName;
    /**
     * 方法参数json 字符串
     */
    private String payload;
    /**
     * 重试次数，默认2
     */
    private Integer retryCount = 2;

    /**
     * 是否等待设备响应，默认false
     */
    private boolean waitResult = false;
    /**
     * 设备连接超时时间,默认10
     */
    private Integer connectTimeoutInSeconds = 10;
    /**
     * 设备响应超时时间，默认10
     */
    private Integer responseTimeoutInSeconds = 10;

    public IotDirectMethodReq(String deviceSn, String methodName) {
        this.deviceSn = deviceSn;
        this.methodName = methodName;
    }
}
