package com.tal.sea.seaover.application.dto.cube;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class AlarmsDetailResponse {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一UUID
     */
    private String unionId;

    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（24小时制 由HH:MM 转为秒级时间）
     */
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;
    /**
     * icon URL
     */
    private String iconUrl;

    /**
     * 铃声 ID
     */
    private String ringId;
    /**
     * 铃声 URL
     */
    private String ringUrl;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否已删除，0-否，1-是
     */
    private Integer deleted;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    private Integer isPreSet;

    /**
     * 同步状态，0-未同步，1-已同步
     */
    private Integer syncStatus;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

}
