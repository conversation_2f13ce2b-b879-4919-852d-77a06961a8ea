package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.dto.version.TbParentAppVersionRequest;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@FeignClient(name = "so-606-device-application", path = "/inner/parent/version",contextId = "tbParentAppVersionFeign")
public interface TbParentAppVersionFeign {

    @PostMapping("/report")
    ResponseEntity report(@Valid @RequestBody TbParentAppVersionRequest request);
}
