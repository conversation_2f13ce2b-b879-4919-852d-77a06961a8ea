package com.tal.sea.seaover.application.dto.cube;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CubeRepeatCheckRespVo {

    ResultEnum resultEnum;

    /**
     * 新增的闹钟id
     */
    private Long id;
    /**
     * 编辑状态
     */
    Boolean modifyResult;
}
