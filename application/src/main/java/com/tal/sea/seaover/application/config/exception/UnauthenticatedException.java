package com.tal.sea.seaover.application.config.exception;

import com.tal.sea.seaover.application.config.result.ResultEnum;

/**
 * 登录验证失败
 *
 * <AUTHOR>
 */
public class UnauthenticatedException extends BaseException {
    public UnauthenticatedException() {
        super(ResultEnum.LOGINCONTEXT_ERROR.val(), ResultEnum.LOGINCONTEXT_ERROR.msg());
    }

    public UnauthenticatedException(String message) {
        super(ResultEnum.LOGINCONTEXT_ERROR.val(), message);
    }

    public UnauthenticatedException(String message, Throwable cause) {
        super(ResultEnum.LOGINCONTEXT_ERROR.val(), message, cause);
    }

    public UnauthenticatedException(int code, String message) {
        super(code, message);
    }

    public UnauthenticatedException(int code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
