package com.tal.sea.seaover.application.config.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;

import java.util.Objects;

@Data
public class JsonResult<T> {
    /**
     * 请求结果编码
     */
    @Schema(description = "响应码  成功：200 其余code码为失败", example = "200")
    private int code;
    /**
     * 请求结果文字说明
     */
    @Schema(description = "返回描述", example = "success")
    private String message;
    /**
     * 响应的业务数据
     */
    @Schema(description = "具体的业务数据对象")
    private T data;

    private JsonResult() {
        this.code = ResultEnum.SUCCESS.val();
        this.message = ResultEnum.SUCCESS.msg();
    }

    private JsonResult(String msg) {
        this.code = ResultEnum.ERROR.val();
        this.message = msg;
    }

    public JsonResult(int code, String msg) {
        this.code = code;
        this.message = msg;
    }

    private JsonResult(ResultEnum errorCode) {
        this.code = errorCode.val();
        this.message = errorCode.msg();
    }

    private JsonResult(T data) {
        this();
        this.data = data;
    }


    /**
     * 业务处理成功,无数据返回
     */
    public static JsonResult<Void> ok() {
        return new JsonResult<>();
    }

    /**
     * 业务处理成功，有数据返回
     */
    public static <T> JsonResult<T> buildSuccessResult(T data) {
        return new JsonResult<>(data);
    }


    /**
     * 业务处理失败
     */
    public static JsonResult<Void> fail(ResultEnum errorCode) {
        return new JsonResult<>(errorCode);
    }


    /**
     * 系统错误
     */
    public static JsonResult<Void> error() {
        return new JsonResult<>(ResultEnum.ERROR);
    }

    public static JsonResult buildErrorResultWithMsg(ResultEnum resultEnum, String resultMsg) {
        JsonResult<Object> result = new JsonResult<>(resultEnum);
        result.setMessage(resultMsg);
        return result;
    }

    /**
     * 判断是否成功
     */
    public boolean isOk() {
        return Objects.equals(this.code, ResultEnum.SUCCESS.val());
    }

    public static <T> JsonResult<T> buildErrorResult(ResultEnum resultEnum) {
        return new JsonResult<>(resultEnum);
    }

    public static <E> JsonResult<E> buildErrorResult(String errorMsg) {
        return new JsonResult<>(errorMsg);
    }


//    public static <E> JsonResult<E> buildSuccessResult(E data) {
//        JsonResult<E> cr = new JsonResult<E>();
//        cr.setData(data);
//        cr.setCode(ResultEnum.SUCCESS.val());
//        cr.setMsg(ResultEnum.SUCCESS.msg());
//        return cr;
//    }

//
//    public JsonResult(int code, String msg) {
//        this.setCode(code);
//        this.setMsg(msg);
//    }
//
////    public JsonResult(int code, String msg, T data) {
////        this.setCode(code);
////        this.setMsg(msg);
////        this.setData(data);
////    }
//
//
//
//
//    public static <E> JsonResult<E> buildErrorResult(String errorMsg) {
//        return buildErrorResult(null, errorMsg);
//    }
//
//    public static <E> JsonResult<E> buildErrorResult(ResultEnum resultEnum) {
//        JsonResult<E> cr = new JsonResult<E>();
//        cr.setData(null);
//        cr.setCode(resultEnum.val());
//        cr.setMsg(resultEnum.msg());
//        return cr;
//    }
//
//    public static <E> JsonResult<E> buildErrorResult(E data, String errorMsg) {
//        JsonResult<E> cr = new JsonResult<E>();
//        cr.setData(data);
//        cr.setCode(ResultEnum.ERROR.val());
//        if (errorMsg == null) {
//            cr.setMsg(ResultEnum.ERROR.msg());
//        } else {
//            cr.setMsg(errorMsg);
//        }
//        return cr;
//    }


}
