package com.tal.sea.seaover.application.config;

import com.tal.sea.seaover.application.config.interceptor.CustomRequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class FeignConfig {

    @Bean
    public CustomRequestInterceptor customRequestInterceptor() {
        return new CustomRequestInterceptor();
    }
}
