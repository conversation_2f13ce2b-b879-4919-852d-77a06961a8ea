package com.tal.sea.seaover.application.config.aop;

import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.util.GsonUtil;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Arrays;

/**
 * 业务执行时间监控
 */
@Slf4j
@Aspect
@Component
public class ServerDiffTimeAspect {

    @Around("execution( * com.tal.sea.seaover.application.controller..*.*(..))")
    public Object handleControllerMethod(ProceedingJoinPoint pjp) throws Throwable {
        Long start = System.currentTimeMillis();
        Object resultVal = null;
        Exception exception = null;
        try {
            initLogMdc();
            resultVal = pjp.proceed();
        } catch (Exception e) {
            exception = e;
            throw e; // 重新抛出异常，确保调用链中的其他处理逻辑能够捕获到异常
        } finally {
            Long end = System.currentTimeMillis();
            long diffTime = end - start;
            // 获取请求路径
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String requestPath = request.getRequestURI();
            // 记录参数
            String paramsStr = formatParams(pjp.getArgs());
            // 记录结果
            String resultStr = formatResult(resultVal);
            // 记录异常信息
            String exceptionStr = (exception != null) ? exception.getMessage() : "No exception";
            log.info("ParentTimeInfo path:【{}】, params:{}, return:{}, exception:{}, time:{}ms",
                    requestPath, paramsStr, resultStr, exceptionStr, diffTime);
        }
        return resultVal;
    }

    private void initLogMdc() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        ThreadMdcUtil.setTraceId(request.getHeader(AppConstant.LOG_TRACE_ID));
        ThreadMdcUtil.setRpcId(request.getHeader(AppConstant.HEADER_RPC_ID));
        ThreadMdcUtil.setTalId(request.getHeader(AppConstant.HEADER_TAL_ID));
        ThreadMdcUtil.setSn(request.getHeader(AppConstant.HEADER_SN));
    }

    private String formatParams(Object[] args) {
        if (args == null || args.length == 0) return "No parameters";
        try {
            return GsonUtil.toJson(args);
        } catch (Exception e) {
            log.warn("Failed to serialize parameters to JSON", e);
            return Arrays.toString(args);
        }
    }

    private String formatResult(Object resultVal) {
        if (resultVal == null) return "No return value";
        try {
            return GsonUtil.toJson(resultVal);
        } catch (Exception e) {
            log.warn("Failed to serialize result to JSON", e);
            return resultVal.toString();
        }
    }

}
