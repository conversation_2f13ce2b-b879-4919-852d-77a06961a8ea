package com.tal.sea.seaover.application.dto.taluser;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class TalUserInfoPlusChild {
    private String family_id;
    private String tal_id;
    private String family_role;
    private String family_role_name;
    private Integer is_manager;

    public enum ManagerState {
        //是否是管理员
        YES(1),
        NO(0);
        private int value;
        ManagerState(int value) {
            this.value = value;
        }
        public int getValue() {
            return value;
        }
    }
}
