package com.tal.sea.seaover.application.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SnInfoRequestVo {
    private String deviceSn;
    private List<String> deviceSnList;
    private String parentTalId;
    //谷歌账号对应的用户id
    private String subId;
    private String talId;
}
