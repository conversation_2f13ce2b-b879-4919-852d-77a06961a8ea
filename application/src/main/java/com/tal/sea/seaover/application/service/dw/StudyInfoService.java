package com.tal.sea.seaover.application.service.dw;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.domain.BizAdsStudyStudentWeek;
import com.tal.sea.seaover.application.dto.studyinfo.StudyInfoReqVo;
import com.tal.sea.seaover.application.dto.studyinfo.StudyInfoRespVo;
import com.tal.sea.seaover.application.mapper.BizAdsStudyStudentWeekMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 */
@Service
public class StudyInfoService {

    @Autowired
    BizAdsStudyStudentWeekMapper adsStudyStudentWeekMapper;

    //@DS("bw-slave")
    public StudyInfoRespVo getStudentWeekStudyInfo(StudyInfoReqVo reqVo) {
        StudyInfoRespVo respVo = new StudyInfoRespVo();
        // 获取本周周一的日期
        String formattedMonday = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        BizAdsStudyStudentWeek studyStudentWeek = adsStudyStudentWeekMapper.selectBizAdsStudyStudentWeekInfo(BizAdsStudyStudentWeek.builder().talId(reqVo.getChildTalId()).model(reqVo.getModel()).weekDate(formattedMonday).build());
        if (studyStudentWeek != null) {
            BeanUtil.copyProperties(studyStudentWeek, respVo);
        }
        // 设置默认值
        setDefaultValues(respVo, reqVo);
        return respVo;
    }

    private void setDefaultValues(StudyInfoRespVo respVo, StudyInfoReqVo reqVo) {
        // 设置默认值，如果已经有值，则保持原样
        respVo.setQuestionCnt(null != respVo.getQuestionCnt() ? respVo.getQuestionCnt() : 0);
        respVo.setKnowledgeCnt(null != respVo.getKnowledgeCnt() ? respVo.getKnowledgeCnt() : 0);
        respVo.setTaskCnt(null != respVo.getTaskCnt() ? respVo.getTaskCnt() : 0);
        respVo.setTotalDuration(null != respVo.getTotalDuration() ? respVo.getTotalDuration() : 0);
        respVo.setSystemDuration(null != respVo.getSystemDuration() ? respVo.getSystemDuration() : 0);
        respVo.setAccurateDuration(null != respVo.getAccurateDuration() ? respVo.getAccurateDuration() : 0);
        respVo.setReadingDuration(null != respVo.getReadingDuration() ? respVo.getReadingDuration() : 0);
        respVo.setToolDuration(null != respVo.getToolDuration() ? respVo.getToolDuration() : 0);
        respVo.setJzxQuestionCorrectRate(null != respVo.getJzxQuestionCorrectRate() ? respVo.getJzxQuestionCorrectRate() : 0);
        respVo.setGameLearnDuration(null != respVo.getGameLearnDuration() ? respVo.getGameLearnDuration() : 0);
        respVo.setTalId(reqVo.getChildTalId());
        respVo.setModel(reqVo.getModel());
    }
}
