package com.tal.sea.seaover.application.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoogleUserInfo {
//    @Schema(description = "用户token", example = "abc123")
//    private String talToken;

    @Schema(description = "用户ID", example = "user123")
    private String talId;

    @Schema(description = "email", example = "")
    private String email;

    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户昵称", example = "小张")
    private String nickname;

}
