package com.tal.sea.seaover.application.dto.cube;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户夜灯设置
 * @TableName tb_night_light_config
 */
@Data
public class NightLightConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 亮度
     */
    private Integer brightness;

    /**
     * 灯效 1常亮 2闪烁效果 3呼吸效果
     */
    private Integer lightingEffects;

    /**
     * 自动照明，0-关闭，1-开启
     */
    private Integer autoLight;

    /**
     * 灯光颜色
     */
    private String color;

    /**
     * 最后修改者，1-device，2-parent
     */
    private Integer lastModifiedBy;

    /**
     * 版本号，服务端内部自增
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}