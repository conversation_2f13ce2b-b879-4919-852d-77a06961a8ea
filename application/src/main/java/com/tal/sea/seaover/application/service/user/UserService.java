package com.tal.sea.seaover.application.service.user;

import com.tal.sea.seaover.application.dto.user.EmailUserInfo;
import com.tal.sea.seaover.application.dto.user.GoogleUserInfo;
import com.tal.sea.seaover.application.dto.user.UserInfo;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public interface UserService {
    /**
     * code 登录
     */
    UserInfo codeLogin(String code, String os);

    /**
     * 获取用户谷歌信息
     */
    GoogleUserInfo queryUserInfo(String talId, String os);

    /**
     * 家长退出登录上报
     */
    void logout(String talId, String os);

    /**
     * 邮箱登录的用户信息
     * @param talId
     * @param os
     * @return
     */
    EmailUserInfo queryUserInfoForEmail(String talId, String os);
}
