package com.tal.sea.seaover.application.dto.control;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class UserFuncListReqVo {
    @Schema(description = "设备SN号", example = "xxx")
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    @Schema(description = "孩子talId", example = "xxx")
    @NotBlank(message = "childTalId  cannot be empty")
    private String childTalId;
}
