package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

import java.util.Date;

@Data
public class CubeAlarmInfoRespVo {
    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 闹钟 ID
     */
    private String talId;

    /**
     * 唯一 UUID
     */
    private String unionId;

    /**
     * 闹钟名称
     */
    private String alarmName;

    /**
     * 闹钟时间（秒级时间）
     */
    private Integer alarmTime;

    /**
     * 重复日（1-7，周一到周日）
     */
    private String repeatDays;

    /**
     * 是否重复（0 - 否，1 - 是）
     */
    private Integer repeating;

    /**
     * 响铃具体日期（仅一次性闹钟需要）
     */
    private String alarmDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * icon URL
     */
    private String iconUrl;

    /**
     * 铃声 ID
     */
    private String ringId;
    /**
     * 铃声 URL
     */
    private String ringUrl;


    /**
     * 是否启用（0 - 否，1 - 是）
     */
    private Integer enabled;

    /**
     * 是否预置闹钟（0 - 否，1 - 是）
     */
    private Integer isPreSet;
}
