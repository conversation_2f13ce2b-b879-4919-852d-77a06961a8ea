package com.tal.sea.seaover.application.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * 学生每周学情表 biz_ads_study_student_week
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizAdsStudyStudentWeek
{
	/** tal_id */
	private String talId;
	/** 型号 */
	private String model;
	/** 本周时间范围 */
	private String weekDate;
	/** 提交题目数 */
	private Integer questionCnt;
	/** 学习知识点数 */
	private Integer knowledgeCnt;
	/** 完成任务数 */
	private Integer taskCnt;
	/** 使用总时长（秒） */
	private Integer totalDuration;
	/** 系统课时长（秒） */
	private Integer systemDuration;
	/** 精准学时长（秒） */
	private Integer accurateDuration;
	/** 阅读时长（秒） */
	private Integer readingDuration;
    /** 学习工具时长（秒） **/
	private Integer toolDuration;
	/** 精准学答题正确率 **/
	private Integer jzxQuestionCorrectRate;
	/** 游戏时长（秒） **/
	private Integer gameLearnDuration;
	/** 创建时间 */
	private Date createdAt;
	/** 更新时间 */
	private Date updatedAt;
}
