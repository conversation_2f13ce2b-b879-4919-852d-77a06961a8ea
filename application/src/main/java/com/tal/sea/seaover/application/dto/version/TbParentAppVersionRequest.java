package com.tal.sea.seaover.application.dto.version;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TbParentAppVersionRequest {

    /**
     * 包名
     */
    @NotBlank(message = "pkgName  cannot be empty")
    private String pkgName;

    /**
     * 用户ID
     */
    @NotBlank(message = "talId  cannot be empty")
    private String talId;

    /**
     * 版本号
     */
    @NotBlank(message = "version  cannot be empty")
    private String version;

    /**
     * 操作系统类型（android/ios）
     */
    @NotBlank(message = "os  cannot be empty")
    private String os;
}
