package com.tal.sea.seaover.application.service.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.constant.RedisConstant;
import com.tal.sea.seaover.application.dto.cube.CubePersonalityInfoVo;
import com.tal.sea.seaover.application.dto.family.FamilyMemberChildInfo;
import com.tal.sea.seaover.application.dto.family.FamilyMemberInfo;
import com.tal.sea.seaover.application.dto.sn.*;
import com.tal.sea.seaover.application.dto.taluser.*;
import com.tal.sea.seaover.application.dto.user.*;
import com.tal.sea.seaover.application.enums.DeviceTypeEnum;
import com.tal.sea.seaover.application.service.common.RedisService;
import com.tal.sea.seaover.application.service.common.TalUserService;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import com.tal.sea.seaover.application.service.feign.UserCenterFeign;
import com.tal.sea.seaover.application.service.user.SnService;
import com.tal.sea.seaover.application.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.tal.sea.seaover.application.constant.RedisConstant.SN_INFO_KEY;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SnServiceImpl implements SnService {

    @Autowired
    private UserCenterFeign userCenterFeign;
    @Autowired
    private TalUserService talUserService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TalParentServerProperties parentServerProperties;
    @Autowired
    private DeviceFeign deviceFeign;

    /**
     * 1 根据talId查询家庭列表
     * 2 根据家庭id获取家庭中孩子-设备关系列表
     * 3 616查询sn信息+设备在离线状态【息屏】
     */
    @Override
    public FamilySnInfo queryFamilySnInfo(String talId, String os) {
        FamilySnInfo familyInfo = FamilySnInfo.builder().parentTalId(talId).build();
        //0、根据talId获取用户信息拿到familyIds
        ServiceResult<TalUserInfo> userInfoData = talUserService.queryUserInfo(talId, os);
        if (!userInfoData.isSuccess() || userInfoData.getData() == null) {
            return familyInfo;
        }
        if (StrUtil.isBlank(userInfoData.getData().getFamily_id())) {
            //当前还未组建家庭
            return familyInfo;
        }
        //1、根据familyIds 循环查家庭成员-登录过的设备列表
        String[] familyIdArray = userInfoData.getData().getFamily_id().split(",");
        //设备Map
        Map<String, FamilySnChildInfo> snMap = new HashMap<>();
        for (String familyId : familyIdArray) {
            //获取家庭成员-设备列表
            ServiceResult<TalFamilyUserAndSnInfo> familyUserSnInfo = talUserService.queryChildAndSnListByFamilyId(familyId, os);
            if (!familyUserSnInfo.isSuccess() || familyUserSnInfo.getData() == null || CollectionUtil.isEmpty(familyUserSnInfo.getData().getHome_list())) {
                continue;
            }
            familyUserSnInfo.getData().getHome_list().forEach(l -> {
                if (StrUtil.isBlank(l.getDevice_sn()) || StrUtil.isBlank(l.getStu_talid())) {
                    return;
                }
                DeviceLocalInfo localInfo = DeviceLocalInfo.builder().deviceSn(l.getDevice_sn()).build();
                //1 去设备服务维护设备的各项信息
                initDeviceInfo(localInfo, talId);

                if (localInfo.getDeviceType() == null) {
                    //fix-当拉不到设备信息时，直接跳过
                    return;
                }
                //2 二次校验设备是否游离
                Boolean loginStateReal = false;
                boolean snState = StrUtil.isNotBlank(l.getDevice_sn()) && l.getLogin_status() != null && l.getLogin_status() == 1;
                if (snState) {
                    ServiceResult<TalDeviceSnBindInfo> talDeviceSnBindInfo = talUserService.queryDeviceBySn(l.getDevice_sn());
                    if (talDeviceSnBindInfo.isSuccess() && talDeviceSnBindInfo.getData() != null && StrUtil.isNotBlank(talDeviceSnBindInfo.getData().getFamily_id()) && familyId.equals(talDeviceSnBindInfo.getData().getFamily_id())) {
                        loginStateReal = true;
                    }
                }
                if (localInfo.getDeviceType() == DeviceTypeEnum.PAWPAL.getValue() && !loginStateReal) {
                    //产品单独加逻辑：闹钟退出登录后，不展示在列表中
                    return;
                }
                FamilySnChildInfo snChildInfo = FamilySnChildInfo.builder()
                        .talId(l.getStu_talid())
                        .sn(l.getDevice_sn())
                        .nickName(l.getNickname())
                        .snName(localInfo.getSnName())
                        .snImg(localInfo.getSnImg())
                        .snModel(localInfo.getSnModel())
                        .deviceType(localInfo.getDeviceType())
                        .loginState(loginStateReal)
                        .build();
                FamilySnChildInfo historyInfo = snMap.get(l.getStu_talid() + snChildInfo.getSnModel() + snChildInfo.getDeviceType());
                if (historyInfo == null || (historyInfo != null && snChildInfo.getLoginState())) {
                    //根据用户+型号+设备类型 剔重
                    snMap.put(l.getStu_talid() + snChildInfo.getSnModel() + snChildInfo.getDeviceType(), snChildInfo);
                }
            });
        }
        List<FamilySnChildInfo> childInfos = snMap.values().stream().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(childInfos)) {
            return familyInfo;
        }
        //2、根据sn号获取设备sn对应的设备型号、类型、图片
        for (FamilySnChildInfo childInfo : childInfos) {
            if (!childInfo.getLoginState()) {
                //未登录展示息屏
                childInfo.setOnlineState(false);
                continue;
            }
            //设备在离线状态
            if (childInfo.getDeviceType() == null || childInfo.getDeviceType() == DeviceTypeEnum.TALPAD.getValue()) {
                //学习机逻辑
                DeviceStatusInfoRequestVo reqVo = DeviceStatusInfoRequestVo.builder().deviceSn(childInfo.getSn()).talId(talId).build();
                JsonResult<DeviceStatusInfo> deviceStatusInfoJsonResult = deviceFeign.queryDeviceStatusInfo(reqVo);
                log.info("userCenterFeignInfo 【queryDeviceStatusInfo】 : reqVo: {}, result:{}", reqVo, deviceStatusInfoJsonResult);
                if (deviceStatusInfoJsonResult != null && deviceStatusInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && deviceStatusInfoJsonResult.getData() != null) {
                    DeviceStatusInfo deviceStatusInfo = deviceStatusInfoJsonResult.getData();
                    childInfo.setOnlineState("1".equals(deviceStatusInfo.getScreen()) ? true : false);
                }
            } else if (childInfo.getDeviceType() == DeviceTypeEnum.PAWPAL.getValue()) {
                //闹钟逻辑
                JsonResult<CubePersonalityInfoVo> cubeResult = deviceFeign.queryCubePersonalityInfo(childInfo.getSn());
                log.info("userCenterFeignInfo 【queryDeviceStatusInfo】 : reqVo: {}, result:{}", childInfo.getSn(), cubeResult);
                if (cubeResult != null && cubeResult.getCode() == ResultEnum.SUCCESS.val() && cubeResult.getData() != null) {
                    CubePersonalityInfoVo deviceStatusInfo = cubeResult.getData();
                    childInfo.setOnlineState(deviceStatusInfo.getWifiStatus() == 1 ? true : false);
                }
            }
        }
        familyInfo.setSnInfoList(childInfos);
        return familyInfo;
    }

    private void initDeviceInfo(DeviceLocalInfo localInfo, String talId) {
        //设备相关信息
        String redisVal = redisService.getString(SN_INFO_KEY + localInfo.getDeviceSn());
        if (StrUtil.isNotBlank(redisVal)) {
            SnInfo snInfo = GsonUtil.fromJson(redisVal, SnInfo.class);
            localInfo.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
            localInfo.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
            localInfo.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getDeviceModelName() : "");
            localInfo.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
        } else {
            SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(localInfo.getDeviceSn()).parentTalId(talId).build();
            JsonResult<SnInfo> snInfoJsonResult = deviceFeign.queryDeviceInfo(requestVo.getDeviceSn());
            log.info("deviceFeignInfo 【querySnInfo】 : reqVo: {}, result:{}", requestVo, snInfoJsonResult);
            if (snInfoJsonResult != null && snInfoJsonResult.getCode() == ResultEnum.SUCCESS.val() && snInfoJsonResult.getData() != null) {
                SnInfo snInfo = snInfoJsonResult.getData();
                localInfo.setSnName(StrUtil.isNotBlank(snInfo.getDeviceModel()) ? snInfo.getDeviceModel() : "");
                localInfo.setSnImg(StrUtil.isNotBlank(snInfo.getPictureUrl()) ? snInfo.getPictureUrl() : "");
                localInfo.setSnModel(StrUtil.isNotBlank(snInfo.getDeviceModelName()) ? snInfo.getDeviceModelName() : "");
                localInfo.setDeviceType(snInfo.getDeviceType() != null ? snInfo.getDeviceType() : null);
                redisService.setString(SN_INFO_KEY + localInfo.getDeviceSn(), GsonUtil.toJson(snInfo), 10L, TimeUnit.MINUTES);
            }
        }
    }

    @Override
    public ResultEnum snCheck(String talId, String sn, String os) {
        //1 根据talId获取subId号
//        ServiceResult<TalGoogleUserInfo> googleUserInfo = talUserService.queryUserGoogleInfo(talId, os);
//        if (!googleUserInfo.isSuccess() || googleUserInfo.getData() == null) {
//            return ResultEnum.USER_INNER_ERROR;
//        }
        //2 校验sn号
        SnInfoRequestVo requestVo = SnInfoRequestVo.builder().deviceSn(sn).talId(talId).build();
        JsonResult<SnCheckResponse> snCheckResult = userCenterFeign.checkSn(requestVo);
        log.info("userCenterFeignInfo 【checkSn】 : reqVo: {}, result:{}", requestVo, snCheckResult.toString());
        if (snCheckResult == null || snCheckResult.getCode() != ResultEnum.SUCCESS.val() || snCheckResult.getData() == null) {
            if (snCheckResult != null && snCheckResult.getCode() == ResultEnum.SN_CHECK_ERROR_1.val()) {
                return ResultEnum.SN_CHECK_ERROR_1;
            }
            if (snCheckResult != null && snCheckResult.getCode() == ResultEnum.SN_CHECK_ERROR_2.val()) {
                return ResultEnum.SN_CHECK_ERROR_2;
            }
            if (snCheckResult != null && snCheckResult.getCode() == ResultEnum.SN_CHECK_ERROR_3.val()) {
                return ResultEnum.SN_CHECK_ERROR_3;
            }
            return ResultEnum.USER_INNER_ERROR;
        }
        if (snCheckResult.getData().getValid() != 1) {
            return ResultEnum.SN_ERROR;
        }

        //2 同步状态为绑定中
        BindDeviceReqVo deviceReqVo = BindDeviceReqVo.builder().deviceSn(sn).parentTalId(talId).bindingStatus(BindDeviceReqVo.BindStatusEnum.BINDING.getStatus()).build();
        JsonResult bindStatusResult = userCenterFeign.bindDeviceStatus(deviceReqVo);
        log.info("userCenterFeignInfo 【bindDeviceStatus】 : reqVo: {}, result:{}", deviceReqVo, bindStatusResult.toString());
        if (bindStatusResult == null || bindStatusResult.getCode() != ResultEnum.SUCCESS.val()) {
            return ResultEnum.USER_INNER_ERROR;
        }
        return ResultEnum.SUCCESS;
    }

    @Override
    /**
     * 616
     * sn没被绑定
     *   1 孩子是否有familyId
     *       1 没有   与校验成功
     * 	  2 有
     * 	      1 孩子familyid   有成员，成员是否包括家长
     * 		      家长在家庭里 预校验成功
     * 			  不在  //已经被xxxx绑定
     *
     * sn被绑定
     *    1 sn familyId  和孩子familyId是否相等，不相等的话直接失败 //被xxx绑定
     *    2 相等，拿snfamily查家庭成员列表，包含孩子id和家长id,那就重复绑定，否则失败
     *
     * 用户中心
     * 1 根据孩子 userinfo 查询是否有familyId
     * 2 根据sn查sn所属的家庭 https://yapi.xesv5.com/project/4279/interface/api/131367
     * 3、创建家庭&绑定设备  https://yapi.xesv5.com/project/4279/interface/api/131376
     * 4、加入家庭&绑定设备 https://yapi.xesv5.com/project/4279/interface/api/131385
     * 5、获取家长是否已有家庭关系https://yapi.xesv5.com/project/4279/interface/api/122502
     *
     */
    public SnCodeCheckAndBindResult codeCheckAndBind(CodeCheckBindReqVo reqVo) {
        //1 根据确认码+sn号 获取 校验结果+孩子talId
        CodeCheckReqVo reqVo1 = CodeCheckReqVo.builder().deviceSn(reqVo.getSn()).code(reqVo.getCode()).parentTalId(reqVo.getParentTalId()).build();
        JsonResult<CodeCheckRespVo> codeCheckResult = userCenterFeign.checkCode(reqVo1);
        log.info("userCenterFeignInfo 【checkCode】 : reqVo: {}, result:{}", reqVo1, codeCheckResult.toString());
        if (codeCheckResult == null || codeCheckResult.getCode() != ResultEnum.SUCCESS.val() || codeCheckResult.getData() == null
                || codeCheckResult.getData().getCodeValid() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        if (codeCheckResult.getData().getCodeValid() != 1) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_1).build();
        }
        String childTalId = codeCheckResult.getData().getChildTalId();

        //2、根据sn查sn是否已有所属家庭
        ServiceResult<TalDeviceSnBindInfo> talDeviceSnBindInfo = talUserService.queryDeviceBySn(reqVo.getSn());
        if (!talDeviceSnBindInfo.isSuccess() || talDeviceSnBindInfo.getData() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        String snFamilyId = talDeviceSnBindInfo.getData().getFamily_id();
        //1 根据孩子查询是否有家庭familyId
        ServiceResult<TalUserInfoPlus> talUserInfo = talUserService.queryUserInfoPlus(childTalId, reqVo.getOs());
        if (!talUserInfo.isSuccess() || talUserInfo.getData() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        String childFamilyId = talUserInfo.getData().getFamily_id();
        if (StrUtil.isBlank(snFamilyId)) {
            //sn未被绑定
            if (StrUtil.isNotBlank(childFamilyId)) {
                //判断家庭是否有当前家长
                ServiceResult<FamilyMemberInfo> memberInfoResult = talUserService.queryFamilyMemberById(childFamilyId);
                if (!memberInfoResult.isSuccess() || memberInfoResult.getData() == null) {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
                }
                List<FamilyMemberChildInfo> familyMemberList = Optional.ofNullable(memberInfoResult.getData().getList())
                        .orElseGet(Collections::emptyList);
                //判断家庭是否有当前家长
                Boolean checkIsSameFamily = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(reqVo.getParentTalId()));
                if (!checkIsSameFamily) {
                    //不是一家人
                    for (FamilyMemberChildInfo member : familyMemberList) {
                        if (member != null && member.getIs_manager() == TalUserInfoPlusChild.ManagerState.YES.getValue() && StrUtil.isNotEmpty(member.getTal_id())) {
                            ServiceResult<TalGoogleUserInfo> realParentResult = talUserService.queryUserGoogleInfo(member.getTal_id(), reqVo.getOs());
                            if (realParentResult != null && realParentResult.isSuccess() && realParentResult.getData() != null && StrUtil.isNotEmpty(realParentResult.getData().getEmail())) {
                                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_3).resultMsg(ResultEnum.SN_CODE_CHECK_ERROR_3.msg().replace("XXX", realParentResult.getData().getEmail())).build();
                            }
                            break;
                        }
                    }
                    //保底失败
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_2).build();
                }
            }
        } else {
            //sn被绑定
            if (!snFamilyId.equals(childFamilyId)) {
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_4).build();
            } else {
                //snFamilyId查家庭成员列表，如果包含孩子id和家长id,那就重复绑定，否则失败
                ServiceResult<FamilyMemberInfo> memberInfoResult = talUserService.queryFamilyMemberById(snFamilyId);
                if (!memberInfoResult.isSuccess() || memberInfoResult.getData() == null) {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
                }
                List<FamilyMemberChildInfo> familyMemberList = memberInfoResult.getData().getList();
                //判断家庭是否有当前家长
                Boolean checkIsSameFamily = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(reqVo.getParentTalId()));
                //判断家庭是否有当前孩子
                Boolean checkIsSameFamilyChild = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(childTalId));
                if (checkIsSameFamily && checkIsSameFamilyChild) {
                    //重复绑定，设定标志位
                    String redisSuccessKey = RedisConstant.SN_BIND_REPEAT_SUCCESS_KEY + reqVo.getParentTalId() + reqVo.getSn();
                    redisService.setString(redisSuccessKey, "1", 15L, TimeUnit.SECONDS);
                } else {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_4).build();
                }
            }
        }
        //4、同步状态为校验预成功
        //初始化标志位
        String redisKey = RedisConstant.SN_BIND_WAITING_KEY + reqVo.getParentTalId() + reqVo.getSn();
        redisService.setString(redisKey, AppConstant.X_TAL_BIND_INIT_KEY, 60L, TimeUnit.MINUTES);
        BindDeviceReqVo deviceReqVo2 = BindDeviceReqVo.builder().deviceSn(reqVo.getSn()).parentTalId(reqVo.getParentTalId()).bindingStatus(BindDeviceReqVo.BindStatusEnum.BIND.getStatus()).build();
        JsonResult bindSuccessResult = userCenterFeign.bindDeviceStatus(deviceReqVo2);
        log.info("userCenterFeignInfo 【bindDeviceStatus】 : reqVo: {}, result:{}", deviceReqVo2, bindSuccessResult.toString());
        if (bindSuccessResult == null || bindSuccessResult.getCode() != ResultEnum.SUCCESS.val()) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }

        //5、轮询等待客户端发起确认绑定
        long startTime = System.currentTimeMillis();
        while ((System.currentTimeMillis() - startTime) < parentServerProperties.getBindWaitingTime()) {
            String currentValue = redisService.getString(redisKey);
            if (AppConstant.X_TAL_BIND_SUCCESS_KEY.equals(currentValue)) {
                //已绑定完成，返回成功
                redisService.deleteKey(redisKey);
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SUCCESS).build();
            } else if (AppConstant.X_TAL_BIND_ERROR_KEY.equals(currentValue)) {
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
            }
            try {
                Thread.sleep(100); // 避免CPU空转
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        //超时失败
        return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_TIMEOUT_ERROR).build();
    }


    @Override
    public SnCodeCheckAndBindResult codeCheckAndBindV2(CodeCheckBindReqVo reqVo) {
        //1 根据确认码+sn号 获取 校验结果+孩子talId
        CodeCheckReqVo reqVo1 = CodeCheckReqVo.builder().deviceSn(reqVo.getSn()).code(reqVo.getCode()).parentTalId(reqVo.getParentTalId()).build();
        JsonResult<CodeCheckRespVo> codeCheckResult = userCenterFeign.checkCode(reqVo1);
        log.info("userCenterFeignInfo 【checkCode】 : reqVo: {}, result:{}", reqVo1, codeCheckResult.toString());
        if (codeCheckResult == null || codeCheckResult.getCode() != ResultEnum.SUCCESS.val() || codeCheckResult.getData() == null
                || codeCheckResult.getData().getCodeValid() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        if (codeCheckResult.getData().getCodeValid() != 1) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_1).build();
        }
        String childTalId = codeCheckResult.getData().getChildTalId();

        //2、根据sn查sn是否已有所属家庭
        ServiceResult<TalDeviceSnBindInfo> talDeviceSnBindInfo = talUserService.queryDeviceBySn(reqVo.getSn());
        if (!talDeviceSnBindInfo.isSuccess() || talDeviceSnBindInfo.getData() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        String snFamilyId = talDeviceSnBindInfo.getData().getFamily_id();
        //1 根据孩子查询是否有家庭familyId
        ServiceResult<TalUserInfoPlus> talUserInfo = talUserService.queryUserInfoPlus(childTalId, reqVo.getOs());
        if (!talUserInfo.isSuccess() || talUserInfo.getData() == null) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }
        String childFamilyId = talUserInfo.getData().getFamily_id();
        if (StrUtil.isBlank(snFamilyId)) {
            //sn未被绑定
            if (StrUtil.isNotBlank(childFamilyId)) {
                //判断家庭是否有当前家长
                ServiceResult<FamilyMemberInfo> memberInfoResult = talUserService.queryFamilyMemberById(childFamilyId);
                if (!memberInfoResult.isSuccess() || memberInfoResult.getData() == null) {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
                }
                List<FamilyMemberChildInfo> familyMemberList = Optional.ofNullable(memberInfoResult.getData().getList())
                        .orElseGet(Collections::emptyList);
                //判断家庭是否有当前家长
                Boolean checkIsSameFamily = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(reqVo.getParentTalId()));
                if (!checkIsSameFamily) {
                    //不是一家人
                    for (FamilyMemberChildInfo member : familyMemberList) {
                        if (member != null && member.getIs_manager() == TalUserInfoPlusChild.ManagerState.YES.getValue() && StrUtil.isNotEmpty(member.getTal_id())) {
                            ServiceResult<TalUserInfoPlus> realParentResult = talUserService.queryUserInfoPlus(member.getTal_id(), reqVo.getOs());
                            if (realParentResult != null && realParentResult.isSuccess() && realParentResult.getData() != null && StrUtil.isNotEmpty(realParentResult.getData().getEmail())) {
                                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_3).resultMsg(ResultEnum.SN_CODE_CHECK_ERROR_3.msg().replace("XXX", realParentResult.getData().getEmail())).build();
                            }
                            break;
                        }
                    }
                    //保底失败
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_2).build();
                }
            }
        } else {
            //sn被绑定
            if (!snFamilyId.equals(childFamilyId)) {
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_4).build();
            } else {
                //snFamilyId查家庭成员列表，如果包含孩子id和家长id,那就重复绑定，否则失败
                ServiceResult<FamilyMemberInfo> memberInfoResult = talUserService.queryFamilyMemberById(snFamilyId);
                if (!memberInfoResult.isSuccess() || memberInfoResult.getData() == null) {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
                }
                List<FamilyMemberChildInfo> familyMemberList = memberInfoResult.getData().getList();
                //判断家庭是否有当前家长
                Boolean checkIsSameFamily = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(reqVo.getParentTalId()));
                //判断家庭是否有当前孩子
                Boolean checkIsSameFamilyChild = familyMemberList.stream().anyMatch(l -> l.getTal_id().equals(childTalId));
                if (checkIsSameFamily && checkIsSameFamilyChild) {
                    //重复绑定，设定标志位
                    String redisSuccessKey = RedisConstant.SN_BIND_REPEAT_SUCCESS_KEY + reqVo.getParentTalId() + reqVo.getSn();
                    redisService.setString(redisSuccessKey, "1", 15L, TimeUnit.SECONDS);
                } else {
                    return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_ERROR_4).build();
                }
            }
        }
        //4、同步状态为校验预成功
        //初始化标志位
        String redisKey = RedisConstant.SN_BIND_WAITING_KEY + reqVo.getParentTalId() + reqVo.getSn();
        redisService.setString(redisKey, AppConstant.X_TAL_BIND_INIT_KEY, 60L, TimeUnit.MINUTES);
        BindDeviceReqVo deviceReqVo2 = BindDeviceReqVo.builder().deviceSn(reqVo.getSn()).parentTalId(reqVo.getParentTalId()).bindingStatus(BindDeviceReqVo.BindStatusEnum.BIND.getStatus()).build();
        JsonResult bindSuccessResult = userCenterFeign.bindDeviceStatus(deviceReqVo2);
        log.info("userCenterFeignInfo 【bindDeviceStatus】 : reqVo: {}, result:{}", deviceReqVo2, bindSuccessResult.toString());
        if (bindSuccessResult == null || bindSuccessResult.getCode() != ResultEnum.SUCCESS.val()) {
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
        }

        //5、轮询等待客户端发起确认绑定
        long startTime = System.currentTimeMillis();
        while ((System.currentTimeMillis() - startTime) < parentServerProperties.getBindWaitingTime()) {
            String currentValue = redisService.getString(redisKey);
            if (AppConstant.X_TAL_BIND_SUCCESS_KEY.equals(currentValue)) {
                //已绑定完成，返回成功
                redisService.deleteKey(redisKey);
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SUCCESS).build();
            } else if (AppConstant.X_TAL_BIND_ERROR_KEY.equals(currentValue)) {
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.USER_INNER_ERROR).build();
            }
            try {
                Thread.sleep(100); // 避免CPU空转
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        //超时失败
        return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SN_CODE_CHECK_TIMEOUT_ERROR).build();
    }

    /**
     * 更新角色接口 https://yapi.xesv5.com/project/4279/interface/api/132204
     *
     * @param talId  家长talId
     * @param roleId 角色Id
     * @param os
     * @return
     */
    @Override
    public ResultEnum updateFamilyRoleInfo(String talId, Integer roleId, String os) {
        //获取familyId
        ServiceResult<TalUserInfo> parentUserInfo = talUserService.queryUserInfo(talId, os);
        if (!parentUserInfo.isSuccess() || parentUserInfo.getData() == null || StrUtil.isBlank(parentUserInfo.getData().getFamily_id())) {
            return ResultEnum.USER_INNER_ERROR;
        }
        ServiceResult updatedResult = talUserService.updateFamilyRoleInfo(talId, roleId, parentUserInfo.getData().getFamily_id());
        if (!updatedResult.isSuccess()) {
            return ResultEnum.USER_INNER_ERROR;
        }
        return ResultEnum.SUCCESS;
    }

    @Override
    public SnCodeCheckAndBindResult innerBind(InnerBindReqVo reqVo) {
        String redisKey = RedisConstant.SN_BIND_WAITING_KEY + reqVo.getParentTalId() + reqVo.getSn();
        //校验重复绑定逻辑
        String redisSuccessKey = RedisConstant.SN_BIND_REPEAT_SUCCESS_KEY + reqVo.getParentTalId() + reqVo.getSn();
        String successState = redisService.getString(redisSuccessKey);
        if (StrUtil.isNotBlank(successState)) {
            log.info("innerBind repeat bind reqVo{}", reqVo);
            redisService.deleteKey(redisSuccessKey);
            redisService.setString(redisKey, AppConstant.X_TAL_BIND_SUCCESS_KEY, 60L, TimeUnit.MINUTES);
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SUCCESS).build();
        }

        //1、获取家长是否已有家庭关系https://yapi.xesv5.com/project/4279/interface/api/122502
        ServiceResult<TalUserInfo> parentUserInfo = talUserService.queryUserInfoWithOutOs(reqVo.getParentTalId());
        if (!parentUserInfo.isSuccess() || parentUserInfo.getData() == null) {
            redisService.setString(redisKey, AppConstant.X_TAL_BIND_ERROR_KEY, 60L, TimeUnit.MINUTES);
            return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.CREATE_FAMILY_INNER__ERROR).build();
        }
        //2、创建家庭&绑定设备 https://yapi.xesv5.com/project/4279/interface/api/131376
        String familyId = null;
        if (StrUtil.isBlank(parentUserInfo.getData().getFamily_id())) {
            ServiceResult<TalCreateFamilyAndBindSnInfo> createResult = talUserService.createFamilyAndBindSn(reqVo.getParentTalId(), reqVo.getChildTalId(), reqVo.getSn());
            if (!createResult.isSuccess() || createResult.getData() == null) {
                redisService.setString(redisKey, AppConstant.X_TAL_BIND_ERROR_KEY, 60L, TimeUnit.MINUTES);
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.CREATE_FAMILY_INNER__ERROR).build();
            }
            familyId = createResult.getData().getFamily_id();
        } else {
            //3、加入家庭&绑定设备 https://yapi.xesv5.com/project/4279/interface/api/131385
            familyId = parentUserInfo.getData().getFamily_id();
            ServiceResult<TalJoinFamilyAndBindSnInfo> joinResult = talUserService.joinFamilyAndBindSn(reqVo.getChildTalId(), reqVo.getSn(), familyId);
            if (!joinResult.isSuccess() || joinResult.getData() == null) {
                redisService.setString(redisKey, AppConstant.X_TAL_BIND_ERROR_KEY, 60L, TimeUnit.MINUTES);
                return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.CREATE_FAMILY_INNER__ERROR).build();
            }
        }
        //4 绑定成功
        redisService.setString(redisKey, AppConstant.X_TAL_BIND_SUCCESS_KEY, 60L, TimeUnit.MINUTES);
        return SnCodeCheckAndBindResult.builder().resultEnum(ResultEnum.SUCCESS).build();
    }
}
