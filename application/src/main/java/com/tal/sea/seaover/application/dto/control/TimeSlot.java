package com.tal.sea.seaover.application.dto.control;

import com.tal.sea.seaover.application.controller.control.ControlController;

import java.util.*;

/**
 * <AUTHOR>
 */
public class TimeSlot {
    Set<Integer> weekDays; // 1=Monday, ..., 7=Sunday
    int startSeconds;
    int endSeconds;
    public boolean isActive;

    public TimeSlot(EndTimeSlotVo endTimeSlotVo) {
        this.weekDays = new HashSet<>();
        if (endTimeSlotVo.getIs_actived() == 1) {
            this.isActive = true;
            for (String day : endTimeSlotVo.getWeekno().split(",")) {
                this.weekDays.add(Integer.parseInt(day.trim()));
            }
            this.startSeconds = endTimeSlotVo.getStart();
            this.endSeconds = endTimeSlotVo.getEnd();
            if (this.endSeconds < this.startSeconds) {
                this.endSeconds += 86400;
            }
        } else {
            this.isActive = false;
        }
    }

    public boolean overlapsWith(TimeSlot other) {
        //产品要求关掉生效校验
//        if (!this.isActive || !other.isActive) {
//            return false;
//        }

        //1 判断两个集合是否没有共同的元素 如果两个集合没有任何交集，即它们之间没有相同的元素，那么该方法返回 true；否则返回 false
        boolean dayOverlap = Collections.disjoint(this.weekDays, other.weekDays);
        if (!dayOverlap) {
            return true;
        }

        Set<Integer> thisDays = new HashSet<>(this.weekDays);
        Set<Integer> otherDays = new HashSet<>(other.weekDays);

        //1 判断this的每一天和other是否存在冲突
        for (Integer day : thisDays) {
            int thisEnd = this.endSeconds;
            int otherStart = other.startSeconds;
            Integer nextDay = (day == 7 ? 1 : day + 1);
            if (this.endSeconds > 86400 && other.weekDays.contains(nextDay)) {
                thisEnd -= 86400;
                if (thisEnd >= otherStart) {
                    return true;
                }
            }
        }
        //1 判断other的每一天和this是否存在冲突
        for (Integer day : otherDays) {
            int otherEnd = other.endSeconds;
            int thisStart = this.startSeconds;
            Integer nextDay = (day == 7 ? 1 : day + 1);
            if (other.endSeconds > 86400 && this.weekDays.contains(nextDay)) {
                otherEnd -= 86400;
                if (otherEnd >= thisStart) {
                    return true;
                }
            }
        }
        return false;
    }
}
