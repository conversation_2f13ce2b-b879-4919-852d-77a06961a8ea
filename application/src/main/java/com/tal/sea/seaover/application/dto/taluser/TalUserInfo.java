package com.tal.sea.seaover.application.dto.taluser;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class TalUserInfo {
    private int id;

    private Long created_at;

    private Long updated_at;

    private String tal_id;

    private String family_id;

    private String account_name;
    private String nickname;
    private String udc_nickname;

    private String username;
    private String stu_no;
    private String email;

    private Integer sex;

    private String avatar_url;

    private String skin_id;
    private String school_year;

    private Integer grade;
    private Integer stage;

    private Integer province;

    private String province_name;

    private Integer city;

    private String city_name;

    private Integer county;

    private String county_name;

    private String birthday;
    private String hide_phone;
    private String parent_phone;
    private String contact_phone;

    private Integer role_type;

    private String pwd;


    private String authcode;


    private String cg;


    private String client_id;


    private String belong_client_id;
    private Integer status;

    private Integer create_time;
    private String destroyed_reason;

    private Integer destroyed_at;
    private Integer deleted_at;

    private String introduction;

    private String extra;

    private Integer user_login_type;
}
