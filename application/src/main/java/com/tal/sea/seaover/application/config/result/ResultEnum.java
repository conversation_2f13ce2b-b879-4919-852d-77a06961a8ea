package com.tal.sea.seaover.application.config.result;

/**
 * <AUTHOR>
 * @title: ResultEnum
 * @Description: 返回结果
 */
public enum ResultEnum {
    /**
     * 操作成功
     */
    SUCCESS(200, "success"),

    /**
     * 操作异常
     */
    ERROR(619999, "error"),
    /**
     * 参数异常
     */
    PARAMETER_ERROR(619888, "参数缺失！"),

    LOGINCONTEXT_ERROR(-13, "登录状态验证错误,请重新登录!"),

    /**
     * 业务异常
     * 通用错误
     */
    USER_INNER_ERROR(619001, "Network error, please check the network settings"),


    /**
     * 用户相关
     */
    USER_TYPE_ERROR(619002, "please use parent role login"),

    USER_HAS_NOT_PARENT_ERROR(619003, "user has not parent"),

    /**
     * 设备相关
     */
    SN_ERROR(619101, "sn error"),
    /**
     * 设备SN校验 失败
     */
    SN_CHECK_ERROR_1(616154, "The Thinkpal app and the tablet cannot share the same login account"),
    SN_CHECK_ERROR_2(616155, "SN Error"),
    SN_CHECK_ERROR_3(616156, "Please log in to the learning machine and keep it in a bind-ready state"),

    /**
     * 设备确认码校验失败
     */
    SN_CODE_CHECK_ERROR_1(619201, "Confirmation code error"),
    SN_CODE_CHECK_ERROR_2(619202, "binding status error"),
    SN_CODE_CHECK_ERROR_3(619203, "The child's account has already been bound to XXX. Please log in using the XXX account"),
    SN_CODE_CHECK_ERROR_4(619204, "The device has not been bound or it has already been bound to another phone"),
    BIND_INNER_ERROR(619205, "create family error"),
    SN_CODE_CHECK_TIMEOUT_ERROR(619206, "The binding timed out, please try again"),
    CREATE_FAMILY_INNER__ERROR(619207, "create family error"),

    /**
     * 闹钟已不归属本人
     */
    CUBE_SN_STATE_ERROR(619307, "Operation failed. PawPal account not logged in."),

    /**
     * cube 绑定
     */
    ADD_CHILD_ERROR_1(619301, "Binding Failed"),
    ADD_CHILD_ERROR_2(619302, "Binding Failed"),
    ADD_CHILD_ERROR_3(619303, "Binding Failed"),
    ADD_CHILD_ERROR_4(619304, "Binding Failed"),
    /**
     * 闹钟重复
     */
    CUBE_ALARM_TIME_CONFLICT(619305, "Alarm already exists"),

    /**
     * 日程重复
     */
    CUBE_CALENDAR_TIME_CONFLICT(619306, "Schedule already exists"),

    CUBE_BIND_DEFAULT_ERROR(619311, "Binding Failed"),
    CUBE_CHECK_DEFAULT_ERROR(619312, "Invalid QR code. Please rescan the QR code."),

    CUBE_ALARM_DEFAULT_ERROR(619321, "Small hiccup, try again later."),

    CUBE_ALARM_OFFLINE_ERROR(619322, "Active when device online."),

    NIGHT_LIGHT_DEFAULT_ERROR(619331, "Network Error: Please check your network settings"),

    NIGHT_LIGHT_CUBE_ERROR(619333, "Network Error"),
    /**
     * 夜灯不在线
     */
    NIGHT_LIGHT_OFFLINE_ERROR(619332, "PawPal Z100 Offline");

    private final int val;
    private final String msg;

    ResultEnum(int val, String msg) {
        this.val = val;
        this.msg = msg;
    }

    public int val() {
        return this.val;
    }

    public String msg() {
        return this.msg;
    }
}
