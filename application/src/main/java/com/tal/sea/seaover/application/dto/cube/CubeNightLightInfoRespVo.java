package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

import java.util.Date;

@Data
public class CubeNightLightInfoRespVo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 亮度
     */
    private Integer brightness;

    /**
     * 灯效 1常亮 2闪烁效果 3呼吸效果
     */
    private Integer lightingEffects;

    /**
     * 自动照明，0-关闭，1-开启
     */
    private Integer autoLight;

    /**
     * 灯光颜色
     */
    private String color;
}
