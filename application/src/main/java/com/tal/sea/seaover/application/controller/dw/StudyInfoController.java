package com.tal.sea.seaover.application.controller.dw;

import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.studyinfo.CubeHabitInfoVo;
import com.tal.sea.seaover.application.dto.studyinfo.StudyInfoReqVo;
import com.tal.sea.seaover.application.dto.studyinfo.StudyInfoRespVo;
import com.tal.sea.seaover.application.service.common.AlarmXtqService;
import com.tal.sea.seaover.application.service.cube.CubeService;
import com.tal.sea.seaover.application.service.dw.CubeHabitService;
import com.tal.sea.seaover.application.service.dw.StudyInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.tal.sea.seaover.application.config.result.ResultEnum.CUBE_SN_STATE_ERROR;
import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;
import static com.tal.sea.seaover.application.constant.AppConstant.X_TAL_OS;

/**
 * 学生每周学情查询
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/study")
@Tag(name = "家长端学生学情管理", description = "学生每周学情查询")
public class StudyInfoController {

    @Autowired
    StudyInfoService studyInfoService;
    @Autowired
    private CubeHabitService cubeHabitService;
    @Autowired
    private CubeService cubeService;
    @Autowired
    private AlarmXtqService alarmXtqService;

    @Operation(summary = "获取学生每周学情信息", description = "")
    @PostMapping("/weekInfo")
    public JsonResult<StudyInfoRespVo> weekInfo(@Validated @RequestBody StudyInfoReqVo reqVo) {
        StudyInfoRespVo respVo = studyInfoService.getStudentWeekStudyInfo(reqVo);
        return JsonResult.buildSuccessResult(respVo);
    }

    @Operation(summary = "获取闹钟习惯报告", description = "")
    @GetMapping("/cube/habit")
    public JsonResult<List<CubeHabitInfoVo>> cubeHabitInfo(
            @RequestHeader(value = X_TAL_OS) String os,
            @RequestParam String talId,
            @RequestParam String sn) {
        // 检查用户权限
        if (!cubeService.checkCubeSnState(sn, talId, os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        List<CubeHabitInfoVo> cubeHabitInfo = cubeHabitService.getCubeHabitInfo(talId, sn);
        return JsonResult.buildSuccessResult(cubeHabitInfo);
    }

    @ExceptionHandler(Exception.class)
    public JsonResult handleException(Exception e) {
        alarmXtqService.alarmAndLogForError("StudyInfoController error: " +e.getMessage(), e);
        return JsonResult.buildErrorResult(USER_INNER_ERROR);
    }


}
