package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeCalendarDeleteReqVo {
    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * scheduleId
     */
    @NotNull(message = "scheduleId cannot be empty")
    private Long scheduleId;
}
