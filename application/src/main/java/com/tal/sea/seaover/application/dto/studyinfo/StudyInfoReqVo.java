package com.tal.sea.seaover.application.dto.studyinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class StudyInfoReqVo {
    @Schema(description = "孩子talId", example = "Talxxxxxxx")
    @NotEmpty
    private String childTalId;

    @Schema(description = "设备型号", example = "T100")
    @NotEmpty
    private String model;
}
