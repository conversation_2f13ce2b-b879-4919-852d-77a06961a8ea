package com.tal.sea.seaover.application.enums;

/**
 * 0-未知 1-妈妈 2-爸爸 3-爷爷 4-奶奶 5-外公 6-外婆 7-祖父 8-祖母 98-家人 99-孩子
 *
 * <AUTHOR>
 */
public enum RoleTypeEnum {

    NO(0, "未知"),

    MOTHER(1, "妈妈"),

    FATHER(2, "爸爸"),

    GRADE_FATHER(3, "祖父"),

    GRADE_MOTHER(4, "祖母"),

    FAMILY_USER(98, "监护人"),

    STUDENT(99, "孩子"),

    ADMIN(1, "管理员");

    private final int val;
    private final String msg;

    RoleTypeEnum(int val, String msg) {
        this.val = val;
        this.msg = msg;
    }

    public int val() {
        return this.val;
    }

    public String msg() {
        return this.msg;
    }
}
