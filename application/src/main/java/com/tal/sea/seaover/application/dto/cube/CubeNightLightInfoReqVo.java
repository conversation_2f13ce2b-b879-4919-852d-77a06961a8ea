package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class CubeNightLightInfoReqVo {
    /**
     * 夜灯ID
     */
    private Long nightLightConfigId;
    /**
     * 孩子talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;
}
