package com.tal.sea.seaover.application.dto.talcontrol;   // 解决package

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ControlSubRequest {
    private String suite_key;
    private String unit_key;
    private String unit_value;

}