package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeCalendarModifyReqVo {
    /**
     * 主键
     */
    @NotNull(message = "id cannot be empty")
    private Integer id;
    /**
     * tal_id
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;

    /**
     * sn
     */
    @NotBlank(message = "sn cannot be empty")
    private String sn;

    /**
     * 唯一UUID
     */
    @NotBlank(message = "unionId cannot be empty")
    private String unionId;

    /**
     * 日程名称
     */
    @NotBlank(message = "name cannot be empty")
    private String name;

    /**
     * 日程时间（24小时制 由HH:MM 转为秒级时间）
     */
    @NotNull(message = "scheduleTime cannot be empty")
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    @NotNull(message = "notifyDuration cannot be empty")
    private Integer notifyDuration;

    /**
     * 重复日（1-7，周一到周日）格式: 1,4,5
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    @NotNull(message = "repeating cannot be empty")
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性闹钟需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    @NotBlank(message = "iconId cannot be empty")
    private String iconId;

    /**
     * label颜色
     */
    @NotBlank(message = "colour cannot be empty")
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    @NotNull(message = "enabled cannot be empty")
    private Integer enabled;

    /**
     * 是否预置闹钟 0-否，1-是
     */
    @NotNull(message = "isPreSet cannot be empty")
    private Integer isPreSet;
    
}
