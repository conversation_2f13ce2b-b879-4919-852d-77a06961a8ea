package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备端留言响应
 */
@Data
public class DeviceRecordingResponse implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一标识
     */
    private String uuid;

    /**
     * 用户Id
     */
    private String talId;

    /**
     * 设备sn
     */
    private String sn;

    /**
     * 名字
     */
    private String name;

    /**
     * 音频文件URL
     */
    private String url;

    /**
     * 音频时长(s)
     */
    private Integer duration;

    /**
     * 发送类型 0-即时发送 1-定时发送
     */
    private Integer sendType;

    /**
     * 定时发送时间
     */
    private Long scheduledTime;

    /**
     * 发送状态 0-未发送 1-已发送
     */
    private Integer sendStatus;

    /**
     * 设备接收状态 0-未接收 1-已接收
     */
    private Integer deviceReceiveStatus;

    /**
     * 收听状态 0-未收听 1-已收听
     */
    private Integer listenStatus;

    /**
     * 录制时间
     */
    private Long recordTime;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 删除状态 0-未删除 1-已删除
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;
}
