package com.tal.sea.seaover.application.dto.sn;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CodeCheckBindReqVo {

    @Schema(description = "设备sn号", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "sn  cannot be empty")
    String sn;
    @Schema(description = "设备确认码", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "code  cannot be empty")
    String code;
    @Schema(description = "家长TalId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String parentTalId;
    @Schema(description = "os系统", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    String os;
    @Schema(description = "孩子talId")
    String childTalId;
}
