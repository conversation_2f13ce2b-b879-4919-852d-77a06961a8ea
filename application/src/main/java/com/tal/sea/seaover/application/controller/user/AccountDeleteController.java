package com.tal.sea.seaover.application.controller.user;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.family.*;
import com.tal.sea.seaover.application.service.user.AccountDeleteService;
import com.tal.sea.seaover.application.service.user.FamilyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.constant.AppConstant.HEADER_TAL_ID;
import static com.tal.sea.seaover.application.constant.AppConstant.X_TAL_OS;

/**
 * 家庭账号删除管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/account/delete/")
public class AccountDeleteController {

    @Autowired
    private AccountDeleteService accountDeleteService;

    /**
     * 待删除页详情数据获取
     *
     * @param os
     * @param talId
     * @return
     */
    @GetMapping("/info")
    public JsonResult<AccountDeleteInfo> getAccountDeleteInfo(@RequestHeader(value = X_TAL_OS) String os,
                                                              @RequestHeader(value = HEADER_TAL_ID) String talId) {
        AccountDeleteInfo info = accountDeleteService.getAccountDeleteInfo(talId, os);
        if (info == null) {
            return JsonResult.buildErrorResult(USER_INNER_ERROR);
        }
        return JsonResult.buildSuccessResult(info);
    }

    /**
     * 账号删除请求提交
     *
     * @param os
     * @param talId
     * @return
     */
    @GetMapping("/submit")
    public JsonResult deleteAccountSubmit(@RequestHeader(value = X_TAL_OS) String os,
                                          @RequestHeader(value = HEADER_TAL_ID) String talId) {
        //内部调用616进行删除
        ResultEnum result = accountDeleteService.deleteAccountSubmit(talId, os);
        if (result != SUCCESS) {
            return JsonResult.buildErrorResult(result);
        }
        return JsonResult.buildSuccessResult(null);
    }

    /**
     * 账号删除后重新激活
     *
     * @param os
     * @param talId
     * @return
     */
    @GetMapping("/activate")
    public JsonResult deleteAccountActivate(@RequestHeader(value = X_TAL_OS) String os,
                                            @RequestHeader(value = HEADER_TAL_ID) String talId) {
        //内部调用616进行删除激活
        ResultEnum result = accountDeleteService.deleteAccountActivate(talId, os);
        if (result != SUCCESS) {
            return JsonResult.buildErrorResult(result);
        }
        return JsonResult.buildSuccessResult(null);
    }


}
