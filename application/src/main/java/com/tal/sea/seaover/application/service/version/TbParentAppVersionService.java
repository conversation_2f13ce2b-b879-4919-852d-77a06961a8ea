package com.tal.sea.seaover.application.service.version;

import com.tal.sea.seaover.application.dto.version.TbParentAppVersionRequest;
import com.tal.sea.seaover.application.service.feign.TbParentAppVersionFeign;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Enumeration;

import static com.tal.sea.seaover.application.constant.AppConstant.*;

@Slf4j
@Service
public class TbParentAppVersionService {
    @Resource
    private TbParentAppVersionFeign tbParentAppVersionFeign;

    @Async
    public void report(HttpServletRequest request,String traceId) {
        ThreadMdcUtil.setTraceId(traceId);
        log.info("begin report parent app version ");
        try {
            String talId = request.getHeader(HEADER_TAL_ID);
            String os = request.getHeader(X_TAL_OS);
            String version = request.getHeader(HEADER_APP_VERSION);
            String pkgName = request.getHeader(HEADER_APP_PKG_NAME);
            if (null == talId || null == os || null == version || null == pkgName) {
                log.error("report parent app version error, talId:{}, os:{}, version:{}, pkgName:{}", talId, os, version, pkgName);
                return;
            }
            TbParentAppVersionRequest tbParentAppVersionRequest = TbParentAppVersionRequest.builder().
                    talId(talId)
                    .os(os)
                    .version(version)
                    .pkgName(pkgName)
                    .build();
            ResponseEntity report = tbParentAppVersionFeign.report(tbParentAppVersionRequest);
            if (report.getCode() == 200) {
                log.info("report parent app version success, talId:{}, os:{}, version:{}, pkgName:{}", talId, os, version, pkgName);
            } else {
                log.error("report parent app version fail, talId:{}, os:{}, version:{}, pkgName:{}", talId, os, version, pkgName);
            }
        } catch (Exception e) {
            log.error("report parent app version error", e);
        }
    }
}
