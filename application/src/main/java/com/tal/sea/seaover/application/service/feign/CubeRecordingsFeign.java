package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.dto.cube.*;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

/**
 * Feign client for interacting with the Cube Recordings API
 */
@FeignClient(name = "cube-703-datahub-application", contextId = "cubeRecordingsFeign")
public interface CubeRecordingsFeign {

    /**
     * 查询用户留言接口
     */
    @PostMapping("/inner/datahub/cube/recordings/list")
    ResponseEntity<RecordingListResponse> list(@Valid @RequestBody RecordingListRequest request);

    /**
     * 保存留言接口
     */
    @PostMapping("/inner/datahub/cube/recordings/add")
    ResponseEntity<RecordingResponse> add(@Valid @RequestBody RecordingAddRequest request);

    /**
     * 发送留言接口
     */
    @PostMapping("/inner/datahub/cube/recordings/send")
    ResponseEntity send(@Valid @RequestBody RecordingSendRequest request);

    /**
     * 定时发送留言接口
     */
    @PostMapping("/inner/datahub/cube/recordings/scheduled/send")
    ResponseEntity scheduledSend(@Valid @RequestBody RecordingScheduledSendRequest request);

    /**
     * 修改留言名称
     */
    @PostMapping("/inner/datahub/cube/recordings/modifyName")
    ResponseEntity modifyName(@Valid @RequestBody RecordingModifyNameRequest request);

    /**
     * 删除留言接口
     */
    @PostMapping("/inner/datahub/cube/recordings/delete")
    ResponseEntity delete(@Valid @RequestBody RecordingDeleteRequest request);

}