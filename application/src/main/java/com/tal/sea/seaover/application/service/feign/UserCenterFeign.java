package com.tal.sea.seaover.application.service.feign;

import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.cube.CubeBindAndLoginReqVo;
import com.tal.sea.seaover.application.dto.cube.CubeCancelBindReqVo;
import com.tal.sea.seaover.application.dto.cube.DeviceLoginStatusResponse;
import com.tal.sea.seaover.application.dto.cube.QrCodeCheckReqVo;
import com.tal.sea.seaover.application.dto.user.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 用户中心
 *
 * <AUTHOR>
 */
@FeignClient(name = "so-616-user-aggregation", path = "/inner/user/aggregation")
public interface UserCenterFeign {
    /**
     * 用户服务-code登录，获取talId
     */
    @PostMapping(value = "/family/codeLogin")
    JsonResult<CodeLoginCheckResponse> codeLogin(CodeLoginRequestVo requestVo);

    /**
     * 批量查询sn
     */
    @PostMapping(value = "/device/listInfo")
    JsonResult<List<SnInfo>> querySnInfos(SnInfoRequestVo requestVo);

    /**
     * 单独查询sn
     */
    @Deprecated
    @PostMapping(value = "/device/queryInfo")
    JsonResult<SnInfo> querySnInfo(SnInfoRequestVo requestVo);

    /**
     * 孩子退出登录上报接口
     */
    @PostMapping(value = "/family/logout")
    JsonResult childLogout(FamilyLogOutRequestVo requestVo);

    /**
     * 查询设备亮息屏状态
     */
    @PostMapping(value = "/device/query/screen")
    JsonResult<DeviceStatusInfo> queryDeviceStatusInfo(DeviceStatusInfoRequestVo build);

    /**
     * 校验sn
     */
    @PostMapping(value = "/device/checkSn")
    JsonResult<SnCheckResponse> checkSn(SnInfoRequestVo build);

    /**
     * 校验code
     */
    @PostMapping(value = "/device/checkCode")
    JsonResult<CodeCheckRespVo> checkCode(CodeCheckReqVo req);

    /**
     * 同步绑定状态
     */
    @PostMapping(value = "/device/update/bindingStatus")
    JsonResult bindDeviceStatus(BindDeviceReqVo build);


    // Cube认证相关服务

    /**
     * 家长端扫码校验
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/pawpal/qrcode/scan")
    JsonResult<ScanQrCodeResponse> checkQrCode(QrCodeCheckReqVo reqVo);

    /**
     * 家长端扫码确认
     * @param reqVo
     */
    @PostMapping(value = "/pawpal/qrcode/scan-confirm")
    JsonResult<Boolean> cubeScanConfirm(CubeBindAndLoginReqVo reqVo);

    /**
     * 获取设备端登录状态(家长端轮训)
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/pawpal/device/login-status")
    JsonResult<DeviceLoginStatusResponse> cubeLogin(CubeBindAndLoginReqVo reqVo);
    /**
     * 取消绑定
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/pawpal/qrcode/parent-cancel")
    JsonResult<Boolean> cancelBind(CubeCancelBindReqVo reqVo);
    /**
     * cube退出登录
     * @return
     */
    @PostMapping(value = "/pawpal/token/logout")
    JsonResult<Boolean> cubeLogout(CubeLogoutVo reqVo);

    /**
     * 删除账号提交
     */
    @PostMapping(value = "/family/delete")
    JsonResult deleteAccountSubmit(AccountDeleteReqVo reqVo);

    /**
     * 删除账号登出上报
     */
    @PostMapping(value = "/family/delete/logout")
    JsonResult familyDeleteLogout(FamilyLogOutRequestVo requestVo);

    /**
     * 删除账号后重新激活提交
     */
    @PostMapping(value = "/family/activate")
    JsonResult deleteAccountActivate(AccountDeleteReqVo reqVo);

    /**
     * 个人删除账号登出上报
     */
    @PostMapping(value = "/family/account/destroy")
    JsonResult familyAccountDestroy(AccountDeleteReqVo requestVo);

    /**
     * 个人删除账号激活
     */
    @PostMapping(value = "/family/account/active")
    JsonResult familyAccountActive(FamilyLogOutRequestVo requestVo);

}
