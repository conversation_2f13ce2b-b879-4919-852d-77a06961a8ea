package com.tal.sea.seaover.application.dto.family;

import com.tal.sea.seaover.application.config.result.ResultEnum;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChildAddAndBindRespVo {
    //绑定结果枚举
    ResultEnum resultEnum;

    String resultMsg;

    private String talId;

    private String familyId;
}
