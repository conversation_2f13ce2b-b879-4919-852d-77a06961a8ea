package com.tal.sea.seaover.application.dto.cube;

import lombok.Data;

import java.util.Date;

@Data
public class CubeCalendarInfoRespVo {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * tal_id
     */
    private String talId;

    /**
     * 唯一UUID
     */
    private String unionId;

    /**
     * 日程名称
     */
    private String name;

    /**
     * 日程时间 24小时制的秒数
     */
    private Integer scheduleTime;

    /**
     * 提醒时长 1,5,10,20,30 单位分钟
     */
    private Integer notifyDuration;

    /**
     * 重复日 1-7
     */
    private String repeatDays;

    /**
     * 是否重复，0-否，1-是
     */
    private Integer repeating;

    /**
     * 日程具体日期，仅一次性日程需要
     */
    private String scheduleDay;

    /**
     * icon ID
     */
    private String iconId;

    /**
     * icon Url
     */
    private String iconUrl;

    /**
     * label颜色
     */
    private String colour;

    /**
     * 是否启用，0-否，1-是
     */
    private Integer enabled;

    /**
     * 是否预置日程 0-否，1-是
     */
    private Integer isPreSet;


}
