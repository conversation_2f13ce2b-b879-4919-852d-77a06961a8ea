package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyChildUserInfo {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "user123")
    private String talId;
    /**
     * 用户头像
     */
    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String avatarUrl;
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "小张")
    private String nickname;
    /**
     * 用户年级
     */
    @Schema(description = "用户年级")
    private Long grade;
    /**
     * 用户性别
     */
    @Schema(description = "用户性别")
    private Integer sex;
}