package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class CubeBindAndLoginReqVo {
    /**
     * 孩子TalId
     */
    @NotBlank(message = "childId  cannot be empty")
    private String childId;
    /**
     * 设备sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    /**
     * 临时token
     */
    @NotBlank(message = "tempToken  cannot be empty")
    private String tempToken;
    /**
     * 家长id
     */
    private String parentId;

}
