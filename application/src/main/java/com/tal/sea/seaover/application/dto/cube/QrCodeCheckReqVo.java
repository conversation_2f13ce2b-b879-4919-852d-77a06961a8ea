package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class QrCodeCheckReqVo {
    /**
     * 二维码内容
     */
    @NotBlank(message = "qrCodeId  cannot be empty")
    private String qrCodeId;
    /**
     * 家长ID，前端不需要传，后端根据token解析
     */
    private String parentId;
    /**
     * 家长邮箱，前端不需要传，后端自行获取
     */
    private String parentEmail;
}
