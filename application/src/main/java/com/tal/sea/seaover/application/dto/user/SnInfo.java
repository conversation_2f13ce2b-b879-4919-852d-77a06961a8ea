package com.tal.sea.seaover.application.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SnInfo {
    //设备sn
    private String deviceSn;

    //学习机 0 cube 1
    private Integer deviceType;

    //设备展示型号
    private String deviceModel;

    //设备固有型号-固定为T100
    private String deviceModelName;

    private String pictureUrl;

    private Long createTime;

    private Long updateTime;
}