package com.tal.sea.seaover.application.config.interceptor;

import com.tal.sea.seaover.application.constant.AppConstant;
import com.tal.sea.seaover.application.util.ThreadMdcUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * feign 拦截器，在header头中统一新增traceID
 * <AUTHOR>
 */
public class CustomRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        //头部信息里统一新增tradeId
        template.header(AppConstant.LOG_TRACE_ID, ThreadMdcUtil.getTraceId());
    }
}
