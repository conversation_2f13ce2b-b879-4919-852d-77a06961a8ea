package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 编辑夜灯配置请求
 */
@Data
public class NightLightConfigEditRequest {
    @NotNull(message = "id  cannot be empty")
    private Long id;
    @NotBlank(message = "talId  cannot be empty")
    private String talId;
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    @NotBlank(message = "unionId  cannot be empty")
    private String unionId;
    @NotNull(message = "brightness  cannot be empty")
    private Integer brightness;
    @NotNull(message = "lightingEffects  cannot be empty")
    private Integer lightingEffects;
    @NotNull(message = "autoLight  cannot be empty")
    private Integer autoLight;
    @NotBlank(message = "color  cannot be empty")
    private String color;
}
