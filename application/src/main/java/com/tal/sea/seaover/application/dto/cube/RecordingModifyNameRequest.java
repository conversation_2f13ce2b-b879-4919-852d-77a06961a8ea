package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 修改留言名称请求
 */
@Data
public class RecordingModifyNameRequest implements Serializable {

    /**
     * 留言唯一标识
     */
    @NotBlank(message = "uuid cannot be blank")
    private String uuid;

    /**
     * 留言名称
     */
    @NotBlank(message = "name cannot be blank")
    private String name;
}
