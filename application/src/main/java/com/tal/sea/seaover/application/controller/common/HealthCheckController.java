package com.tal.sea.seaover.application.controller.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.nacos.registry.NacosRegistration;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.dto.common.SecretGetReqVo;
import com.tal.sea.seaover.application.service.common.CommonService;
import com.tal.sea.xpod.tools.util.ResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.serviceregistry.ServiceRegistry;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.tal.sea.seaover.application.config.result.ResultEnum.USER_INNER_ERROR;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping("/inner")
public class HealthCheckController {

    /**
     * 健康检查-就绪检查：readinessProbe 检查不通过时k8s将从LB上摘除
     * k8s会检查接口的返回是否为200，返回200则代表接口正常
     */
    @GetMapping(value = "/health/available")
    public JsonResult available() throws Exception {
        return JsonResult.buildSuccessResult(null);
    }

    /**
     * 存活探针
     *
     * @return
     */
    @RequestMapping("/health/alive")
    public JsonResult alive() {
        return JsonResult.buildSuccessResult(null);
    }

    @Autowired
    private NacosRegistration registration;
    @Autowired
    private ServiceRegistry serviceRegistry;

    @GetMapping("/discovery/unRegister")
    public JsonResult<String> deregister() {
        try {
            serviceRegistry.deregister(registration);
        } catch (Exception e) {
            return JsonResult.buildErrorResult("deregister fail");
        }
        return JsonResult.buildSuccessResult(null);
    }

}
