package com.tal.sea.seaover.application.dto.family;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyChildInfoV2 {
    /**
     * 孩子ID
     */
    @Schema(description = "用户ID", example = "user123")
    private String talId;
    /**
     * 用户头像
     */
    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String avatarUrl;
    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称", example = "小张")
    private String nickname;
    /**
     * 设备信息列表
     */
    private List<FamilyChildInfoBody> snList;
}