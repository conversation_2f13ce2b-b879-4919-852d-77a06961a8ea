package com.tal.sea.seaover.application.dto.cube;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CubeAlarmStatusReqVo {
    /**
     * 孩子talId
     */
    @NotBlank(message = "talId cannot be empty")
    private String talId;
    /**
     * alarmId
     */
    @NotNull(message = "alarmId  cannot be empty")
    private Long alarmId;


    /**
     * 闹钟时间（秒级时间）
     */
    @NotNull(message = "alarmTime  cannot be empty")
    private Integer alarmTime;

    /**
     * 重复日（格式 1,5,6）
     */
    private String repeatDays;
    /**
     * 响铃具体日期，仅一次性闹钟需要
     */
    private String alarmDay;
    /**
     * sn
     */
    @NotBlank(message = "sn  cannot be empty")
    private String sn;
    /**
     * 是否启用 0 关闭 1开启"
     */
    @NotNull(message = "enabled  cannot be empty")
    private Integer enabled;
}
