package com.tal.sea.seaover.application.controller.cube;

import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.dto.cube.*;
import com.tal.sea.seaover.application.service.common.RedisService;
import com.tal.sea.seaover.application.service.cube.CubeService;
import com.tal.sea.seaover.application.service.feign.DeviceFeign;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

import static com.tal.sea.seaover.application.config.result.ResultEnum.*;
import static com.tal.sea.seaover.application.constant.AppConstant.HEADER_TAL_ID;
import static com.tal.sea.seaover.application.constant.AppConstant.X_TAL_OS;

/**
 * cube闹钟相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/cube")
public class CubeController {

    @Autowired
    CubeService cubeService;
    @Autowired
    DeviceFeign deviceFeign;
    @Autowired
    RedisService redisService;

    /**
     * 闹钟扫描校验接口
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/checkQrCode")
    public JsonResult<QrCodeCheckRespVo> checkQrCode(
            @RequestHeader(value = HEADER_TAL_ID) String talId,
            @RequestHeader(value = X_TAL_OS) String os,
            @Validated @RequestBody QrCodeCheckReqVo reqVo) {
        reqVo.setParentId(talId);
        QrCodeCheckRespVo respVo = cubeService.checkQrCode(reqVo, talId, os);
        if (respVo == null) {
            return JsonResult.buildErrorResult(CUBE_CHECK_DEFAULT_ERROR);
        }
        return JsonResult.buildSuccessResult(respVo);
    }

    /**
     * 取消二维码绑定接口
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/cancelBind")
    public JsonResult<Boolean> cancelBind(@RequestHeader(value = HEADER_TAL_ID) String talId,
                                          @Validated @RequestBody CubeCancelBindReqVo reqVo) {
        reqVo.setParentId(talId);
        return JsonResult.buildSuccessResult(cubeService.cancelBind(reqVo));
    }

    /**
     * 孩子绑定闹钟
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/bindAndLogin")
    public JsonResult bindAndLogin(
            @RequestHeader(value = HEADER_TAL_ID) String talId,
            @Validated @RequestBody CubeBindAndLoginReqVo reqVo) {
        reqVo.setParentId(talId);
        CubeBindAndLoginInnerRespVo innerRespVo = cubeService.bindAndLogin(reqVo);
        if (innerRespVo == null || !innerRespVo.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(innerRespVo.getResultEnum());
        }
        return JsonResult.buildSuccessResult(null);
    }

    /**
     * 轮询闹钟是否登录
     *
     * @param reqVo
     * @return
     */
    @PostMapping("/getCubeLoginStatus")
    public JsonResult<CubeBindAndLoginRespVo> getCubeLoginStatus(
            @Validated @RequestBody CubeBindAndLoginReqVo reqVo) {
        Integer status = cubeService.getCubeLoginStatus(reqVo);
        if (status == null) {
            return JsonResult.buildErrorResult(CUBE_BIND_DEFAULT_ERROR);
        }
        return JsonResult.buildSuccessResult(CubeBindAndLoginRespVo.builder().status(status).build());
    }


    /**
     * 获取cube基本信息
     */
    @PostMapping("/getCubeInfo")
    public JsonResult<CubeInfoRespVo> getCubeInfo(
            @RequestHeader(value = HEADER_TAL_ID) String talId,
            @RequestHeader(value = X_TAL_OS) String os,
            @Validated @RequestBody CubeInfoReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getChildTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeService.getCubeInfo(reqVo, talId));
    }


    /**
     * 获取闹钟列表
     */
    @PostMapping("/getCubeAlarmList")
    public JsonResult<CubeAlarmListRespVo> getCubeAlarmList(@RequestHeader(value = X_TAL_OS) String os,
                                                            @Validated @RequestBody CubeAlarmListReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeService.getCubeAlarmList(reqVo));
    }

    /**
     * 新增闹钟
     */
    @PostMapping("/addCubeAlarm")
    public JsonResult<CubeAlarmAddRespVo> addCubeAlarm(@RequestHeader(value = X_TAL_OS) String os,
                                                       @Validated @RequestBody CubeAlarmAddReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckRespVo cubeAlarmAddRespVo = cubeService.addCubeAlarm(reqVo);
        if (!cubeAlarmAddRespVo.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(cubeAlarmAddRespVo.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(CubeAlarmAddRespVo.builder().id(cubeAlarmAddRespVo.getId()).build());
    }

    /**
     * 修改闹钟
     */
    @PostMapping("/modifyCubeAlarm")
    public JsonResult<Boolean> modifyCubeAlarm(@RequestHeader(value = X_TAL_OS) String os,
                                               @Validated @RequestBody CubeAlarmModifyReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckRespVo modifyResult = cubeService.modifyCubeAlarm(reqVo);
        if (!modifyResult.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(modifyResult.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(modifyResult.getModifyResult());
    }

    /**
     * 删除闹钟
     */
    @PostMapping("/deleteCubeAlarm")
    public JsonResult<Boolean> deleteCubeAlarm(@RequestHeader(value = X_TAL_OS) String os,
                                               @Validated @RequestBody CubeAlarmDeleteReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        Boolean deleteResult = cubeService.deleteCubeAlarm(reqVo);
        if (deleteResult == null || !deleteResult) {
            return JsonResult.buildErrorResult(CUBE_ALARM_DEFAULT_ERROR);
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(deleteResult);
    }

    /**
     * 查询单个闹钟信息
     */
    @PostMapping("/getCubeAlarmInfo")
    public JsonResult<CubeAlarmInfoRespVo> getCubeAlarmInfo(@RequestHeader(value = X_TAL_OS) String os,
                                                            @Validated @RequestBody CubeAlarmInfoReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeAlarmInfoRespVo cubeAlarmInfo = cubeService.getCubeAlarmInfo(reqVo);
        if (cubeAlarmInfo == null) {
            return JsonResult.buildErrorResult(CUBE_ALARM_DEFAULT_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeAlarmInfo);
    }

    /**
     * 修改闹钟状态
     */
    @PostMapping("/modifyCubeAlarmStatus")
    public JsonResult<Boolean> modifyCubeAlarmStatus(@RequestHeader(value = X_TAL_OS) String os,
                                                     @Validated @RequestBody CubeAlarmStatusReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckRespVo result = cubeService.modifyCubeAlarmStatus(reqVo);
        if (!result.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(result.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(result.getModifyResult());
    }

    /**
     * 获取日程列表
     */
    @PostMapping("/getCubeCalendarList")
    public JsonResult<CubeCalendarListRespVo> getCubeCalendarList(@RequestHeader(value = X_TAL_OS) String os,
                                                                  @Validated @RequestBody CubeCalendarListReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeService.getCubeCalendarList(reqVo));
    }

    /**
     * 新增日程
     */
    @PostMapping("/addCubeCalendar")
    public JsonResult<CubeCalendarAddRespVo> addCubeCalendar(@RequestHeader(value = X_TAL_OS) String os,
                                                             @Validated @RequestBody CubeCalendarAddReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckCalendarRespVo result = cubeService.addCubeCalendar(reqVo);
        if (!result.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(result.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(CubeCalendarAddRespVo.builder().id(result.getId()).build());
    }

    /**
     * 修改日程
     */
    @PostMapping("/modifyCubeCalendar")
    public JsonResult<Boolean> modifyCubeCalendar(@RequestHeader(value = X_TAL_OS) String os,
                                                  @Validated @RequestBody CubeCalendarModifyReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckCalendarRespVo result = cubeService.modifyCubeCalendar(reqVo);
        if (!result.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(result.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(result.getModifyResult());
    }

    /**
     * 删除日程
     */
    @PostMapping("/deleteCubeCalendar")
    public JsonResult<Boolean> deleteCubeCalendar(@RequestHeader(value = X_TAL_OS) String os,
                                                  @Validated @RequestBody CubeCalendarDeleteReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        Boolean deleteResult = cubeService.deleteCubeCalendar(reqVo);
        if (deleteResult == null || !deleteResult) {
            return JsonResult.buildErrorResult(CUBE_ALARM_DEFAULT_ERROR);
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(deleteResult);
    }

    /**
     * 查询单个日程信息
     */
    @PostMapping("/getCubeCalendarInfo")
    public JsonResult<CubeCalendarInfoRespVo> getCubeCalendarInfo(@RequestHeader(value = X_TAL_OS) String os,
                                                                  @Validated @RequestBody CubeCalendarInfoReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeCalendarInfoRespVo cubeAlarmInfo = cubeService.getCubeCalendarInfo(reqVo);
        if (cubeAlarmInfo == null) {
            return JsonResult.buildErrorResult(CUBE_ALARM_DEFAULT_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeAlarmInfo);
    }

    /**
     * 修改日程状态
     */
    @PostMapping("/modifyCubeCalendarStatus")
    public JsonResult<Boolean> modifyCubeCalendarStatus(@RequestHeader(value = X_TAL_OS) String os,
                                                        @Validated @RequestBody CubeCalendarStatusReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        CubeRepeatCheckCalendarRespVo result = cubeService.modifyCubeCalendarStatus(reqVo);
        if (!result.getResultEnum().equals(SUCCESS)) {
            return JsonResult.buildErrorResult(result.getResultEnum());
        }
        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("查询cube信息 deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(CUBE_ALARM_OFFLINE_ERROR);
        }
        return JsonResult.buildSuccessResult(result.getModifyResult());
    }

    /**
     * 查询夜灯信息
     */
    @PostMapping("/getCubeNightLightInfo")
    public JsonResult<CubeNightLightInfoRespVo> getCubeNightLightInfo(@RequestHeader(value = X_TAL_OS) String os,
                                                                      @Validated @RequestBody CubeNightLightInfoReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        return JsonResult.buildSuccessResult(cubeService.getCubeNightLightInfo(reqVo));
    }

    /**
     * 夜灯探测唤醒信息
     */
    @PostMapping("/cubeWakeUp")
    public JsonResult cubeWakeUp(@RequestHeader(value = X_TAL_OS) String os,
                                 @Validated @RequestBody CubeWakeUpReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        cubeService.cubeWakeUp(reqVo);
        return JsonResult.buildSuccessResult(null);
    }

    /**
     * 修改夜灯信息
     */
    @PostMapping("/modifyCubeNightLightInfo")
    public JsonResult modifyCubeNightLightInfo(@RequestHeader(value = X_TAL_OS) String os,
                                               @Validated @RequestBody CubeNightLightInfoModifyReqVo reqVo) {
        if (!cubeService.checkCubeSnState(reqVo.getSn(), reqVo.getTalId(), os)) {
            return JsonResult.buildErrorResult(CUBE_SN_STATE_ERROR);
        }
        //0 校验版本，防止设备端到家长端server中版本错乱
        if (!checkNightLightVersion(reqVo)) {
            return JsonResult.buildSuccessResult(null);
        }
        //1 先同步
        NightLightConfig result = cubeService.modifyCubeNightLightInfo(reqVo);
        if (result == null) {
            return JsonResult.buildErrorResult(NIGHT_LIGHT_CUBE_ERROR);
        }

        //2 判断是否联网
        JsonResult<CubePersonalityInfoVo> cubeInfo = deviceFeign.queryCubePersonalityInfo(reqVo.getSn());
        log.info("modifyCubeNightLightInfo deviceSn:{} cubePersonalityInfo: {}", reqVo.getSn(), cubeInfo);
        if (cubeInfo == null || cubeInfo.getCode() != ResultEnum.SUCCESS.val()
                || cubeInfo.getData() == null || cubeInfo.getData().getWifiStatus() == null || cubeInfo.getData().getWifiStatus() != 1) {
            return JsonResult.buildErrorResult(NIGHT_LIGHT_DEFAULT_ERROR);
        }
        return JsonResult.buildSuccessResult(null);
    }

    private boolean checkNightLightVersion(CubeNightLightInfoModifyReqVo reqVo) {
        String redisKey = "cube:night:light" + reqVo.getTalId() + ":" + reqVo.getSn();
        String redisValue = redisService.getString(redisKey);
        if (StrUtil.isBlank(redisValue)) {
            redisService.setString(redisKey, String.valueOf(reqVo.getVersion()), 15, TimeUnit.SECONDS);
            return true;
        }
        //进行时间戳大小比较，Long类型比较
        long compare = Long.parseLong(redisValue) - reqVo.getVersion();
        if (compare > 0) {
            return false;
        }
        //缓存15秒
        redisService.setString(redisKey, String.valueOf(reqVo.getVersion()), 15, TimeUnit.SECONDS);
        return true;
    }

}
