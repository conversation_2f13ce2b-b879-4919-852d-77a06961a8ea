package com.tal.sea.seaover.application.service.feign;
;
import com.tal.sea.xpod.tools.entity.ResponseEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 公共服务Blob相关Feign客户端
 */
@FeignClient(value = "so-601-public-application", contextId = "publicBlobFeign")
@Service
public interface PublicBlobFeign {

    /**
     * 获取临时token
     *
     * @param appId         应用ID
     * @param containerType 容器类型
     * @return 临时token响应
     */
    @GetMapping("/inner/public/blob/getToken")
    ResponseEntity getToken(@RequestParam String appId, @RequestParam Integer containerType);
}
