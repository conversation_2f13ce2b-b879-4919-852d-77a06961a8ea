package com.tal.sea.seaover.application.service.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.tal.sea.seaover.application.config.TalParentServerProperties;
import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import com.tal.sea.seaover.application.constant.RedisConstant;
import com.tal.sea.seaover.application.dto.common.CubeCommonConfig;
import com.tal.sea.seaover.application.dto.common.ParentCommonConfig;
import com.tal.sea.seaover.application.dto.cube.CubeAlarmConfigVo;
import com.tal.sea.seaover.application.dto.cube.CubeResources;
import com.tal.sea.seaover.application.enums.ResourceTypeEnum;
import com.tal.sea.seaover.application.service.feign.CubeControlServerFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CommonService {

    @Autowired
    RedisService redisService;
    @Autowired
    private TalParentServerProperties properties;
    @Autowired
    private CubeControlServerFeign cubeControlServerFeign;
    @Autowired
    private AlarmXtqService alarmXtqService;

    public String getSecret(String talId) {
        String bucket = redisService.getString(RedisConstant.PARENT_TAL_SECRET_RANGE_KEY + talId);
        if (StrUtil.isBlank(bucket)) {
            String secret = generateSecret();
            redisService.setString(RedisConstant.PARENT_TAL_SECRET_RANGE_KEY + talId, secret);
            return secret;
        }
        return bucket;
    }


    private String generateSecret() {
        int tokenLengthMin = 50;
        Random random = new Random();
        int secretLength = random.nextInt(tokenLengthMin - 5) + 5; //5 - 49
        int aRange = tokenLengthMin - secretLength;
        int a = random.nextInt(aRange);
        int b = a + secretLength;
        String secret = a + "_" + b;
        return secret;
    }

    public ParentCommonConfig getCommonConfig() {
        ParentCommonConfig config = new ParentCommonConfig();
        config.setBoyAvatarList(properties.getBoyAvatarList());
        config.setGirlAvatarList(properties.getGirlAvatarList());
        //闹钟配置
        JsonResult<Map<Integer, List<CubeResources>>> listIconAndSound = cubeControlServerFeign.listIconAndSound();
        if (listIconAndSound == null || listIconAndSound.getCode() != ResultEnum.SUCCESS.val() || listIconAndSound.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube闹钟配置[icon-音频]数据失败,  result: %s", listIconAndSound), null);
        } else {
            config.setAlarmIconList(initRealList(listIconAndSound.getData().get(ResourceTypeEnum.ICON.getValue())));
            config.setAlarmSoundList(initRealList(listIconAndSound.getData().get(ResourceTypeEnum.RING.getValue())));
        }
        //日程配置
        JsonResult<Map<Integer, List<CubeResources>>> listScheduleIcon = cubeControlServerFeign.listScheduleIcon();
        if (listScheduleIcon == null || listScheduleIcon.getCode() != ResultEnum.SUCCESS.val() || listScheduleIcon.getData() == null) {
            alarmXtqService.alarmAndLogForError(String.format("查询cube闹钟日程配置[icon]数据失败,  result: %s", listScheduleIcon), null);
        } else {
            config.setScheduleIconList(initRealList(listScheduleIcon.getData().get(ResourceTypeEnum.ICON.getValue())));
        }
        return config;
    }

    private List<CubeCommonConfig> initRealList(List<CubeResources> cubeResources) {
        if(CollectionUtil.isEmpty(cubeResources)){
            return new ArrayList<>();
        }
        List<CubeCommonConfig> list = new ArrayList<>();
        cubeResources.forEach(item -> {
            CubeCommonConfig cubeCommonConfig = new CubeCommonConfig();
            cubeCommonConfig.setFileId(item.getFileId());
            cubeCommonConfig.setUrl(item.getUrl());
            cubeCommonConfig.setName(item.getName());
            list.add(cubeCommonConfig);
        });
        return list;
    }
}
