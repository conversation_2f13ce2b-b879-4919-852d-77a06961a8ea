package com.tal.sea.seaover.application.config.exception;


import com.tal.sea.seaover.application.config.result.JsonResult;
import com.tal.sea.seaover.application.config.result.ResultEnum;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;



/**
 * <AUTHOR>
 * @Description: 异常处理
 */
@Slf4j
@RestControllerAdvice
public class BaseExceptionHandler {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
//    @ExceptionHandler(ConstraintViolationException.class)
//    public JsonResult<Object> handleConstraintViolationException(ConstraintViolationException e) {
//        StringBuilder errorInfo = new StringBuilder();
//        for(ConstraintViolation c:e.getConstraintViolations()) {
//            errorInfo.append(c.getMessage()).append(",");
//        }
//        if(errorInfo.length()>1) {
//            errorInfo.deleteCharAt(errorInfo.length()-1);
//        }
//        return JsonResult.buildErrorResult(errorInfo.toString());
//    }
//
//    @ExceptionHandler(IllegalStateException.class)
//    public JsonResult<Object> handleIllegalStateException(IllegalStateException e) {
//        logger.error("BaseExceptionHandler>>"+e.getMessage(),e);
//        return JsonResult.buildErrorResult(ResultEnum.PARAMETER_ERROR.msg());
//    }

    @ExceptionHandler(BindException.class)
    public JsonResult<Object> handleBindException(BindException e) {
        logger.error("BaseExceptionHandler>>"+e.getMessage(),e);
        StringBuilder errorInfo = new StringBuilder();
        for(FieldError o:e.getFieldErrors()) {
            if("typeMismatch".equalsIgnoreCase(o.getCode())) {
                errorInfo.append(o.getField()).append(ResultEnum.PARAMETER_ERROR.msg()).append(",");
            } else {
                errorInfo.append(o.getDefaultMessage()).append(",");
            }
        }
        if(errorInfo.length()>1) {
            errorInfo.deleteCharAt(errorInfo.length()-1);
        }
        return JsonResult.buildErrorResult(errorInfo.toString());
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public JsonResult<Object> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest req){
        logger.error("BaseExceptionHandler>>"+e.getMessage()+" 请求地址:"+req.getServletPath());
        return JsonResult.buildErrorResult(ResultEnum.PARAMETER_ERROR.msg());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public JsonResult<Object> handleMissingServletRequestParameterException(MissingServletRequestParameterException e,HttpServletRequest req) {
        logger.error("BaseExceptionHandler>>"+e.getMessage()+" 请求地址:"+req.getServletPath());
        return JsonResult.buildErrorResult(ResultEnum.PARAMETER_ERROR.msg());
    }


    @ExceptionHandler(BaseException.class)
    public JsonResult<Object> handleBaseException(BaseException e) {
        logger.error("BaseExceptionHandler>>"+e.getMessage(), e);
        return new JsonResult<>(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(UnauthenticatedException.class)
    public JsonResult<Object> handleUnauthenticatedExceptionn(UnauthenticatedException e) {
        return new JsonResult<>(ResultEnum.LOGINCONTEXT_ERROR.val(), ResultEnum.LOGINCONTEXT_ERROR.msg());
    }


    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public JsonResult<Object> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e){
        return JsonResult.buildErrorResult("错误的http请求类型");
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(Exception e) {
        logger.error("BaseExceptionHandler>>"+e.getMessage(), e);
        return new ResponseEntity<>(JsonResult.buildErrorResult(ResultEnum.ERROR.msg()), HttpStatus.OK);
    }
}
