package com.tal.sea.seaover.application.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoV2 {
    /**
     * 用户token
     */
    private String talToken;
    /**
     * 用户ID
     */
    private String talId;
    /**
     * 用户名
     */
    private String talName;
    /**
     * 用户头像
     */
    private String avatarUrl;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 在家庭中的身份：0-未知 1-妈妈 2-爸爸 3-爷爷 4-奶奶 5-外公 6-外婆 7-祖父 8-祖母 98-家人 99-孩子
     */
    private Integer familyRole;
    /**
     * 秘钥截取开始位置
     */
    private int secretStart;
    /**
     * 秘钥截取结束位置
     */
    private int secretEnd;
    /**
     * 账号状态（1：正常 2: 待删除）
     */
    private Integer status;
}