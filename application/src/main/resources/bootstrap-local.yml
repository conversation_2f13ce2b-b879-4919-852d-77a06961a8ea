#服务器配置
server:
  port: 8082
  servlet:
    context-path: "/parent-server"

spring:
  application:
    name: so-619-parent-server
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      #database: 3
      #password: dr8by1CpjCArPxunJuNmOd5GaGfw379tCAzCaJ4CTwk=
  datasource:
    dynamic:
      primary: bw-slave #默认数仓读库
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        bw-slave: #数仓读库 单独实例
          url: *******************************************************************************************************************************************************************************************
          username: db_sea_pad_dw_ro
          password: t_sdMrCZGmD4TMTF
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-test-query: SELECT 1
            connection-timeout: 30000
            idle-timeout: 600000
            max-lifetime: 1800000
            max-pool-size: 20
            min-idle: 5
#        master: #家长端主库
#          url: ************************************************************************************************************************************************
#          username: root
#          password: 123456
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          hikari:
#            connection-test-query: SELECT 1
#            connection-timeout: 30000
#            idle-timeout: 600000
#            max-lifetime: 1800000
#            max-pool-size: 20
#            min-idle: 5
mybatis-plus:
  configuration:
    #输出日志
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #配置映射规则
    map-underscore-to-camel-case: true #表示支持下划线到驼蜂的映射


#健康检查
management:
  server:
    port: 8082
  metrics:
    tags:
      application: ${spring.application.name}
  endpoints:
    web:
      exposure:
        include: health,prometheus,metrics,loggers,info
  endpoint:
    health:
      show-details: always
#哮天犬监控
sea:
  alarm:
    xiaotianquantaskid: 1463604
    xiaotianquantoken: f67297c33c6fd8fdef5db89900efd76449ca70f9
#日志配置
logging:
  #config: "classpath:log4j2-local.xml"
  level:
    root: INFO
    com.tal.sea.seaover.application: INFO
    com.tal.sea.seaover.application.mapper: DEBUG


# ===== SpringDoc配置 =====#
springdoc:
  api-docs:
    enabled: true

udc:
  url:
    base: http://10.224.130.44:8096
  appId: 940001
  android-clientId: 942201
  android-packageName: com.tal.sea.ai.monitor
  clientId: 942401
  ios-clientId: 942101
  ios-packageName: com.tal.sea.ai.monitor.ios
  appSecret: 210ysf8dx919083mwwzjra4c90qj616t
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC3gET/7AoJzJtVa2y8XfGB6xiSEhYiLUWdLqdGhuxuNuaIIJdS8bJ3LItFL9gONOnrosvl021bRX7ZXWGF5yOA/Dma0Tbl6yWKxKH90jxeUkWPmWyvWY0h1QhEfxS50wkPVLN4xOmQO13CjltuH3b92iOenVbEMbS51XMNWMhI8wIDAQAB

user:
  control-url: http://10.224.130.142:8096
  device-url: http://10.224.129.254:8096
parent:
  familyRoles:
    - id: 2
      chineseName: 父亲
      englishName: Father
    - id: 1
      chineseName: 母亲
      englishName: Mother
    - id: 3
      chineseName: 祖父
      englishName: Grandpa
    - id: 4
      chineseName: 祖母
      englishName: Grandma
    - id: 98
      chineseName: 监护人
      englishName: Guardian
    - id: 0
      chineseName: 其他
      englishName: Other
  push-messages:
    childLogOut:
      appId: 20001
      title: "Unbind"
      body: "The current device has been unlinked by the parent. Please click to log in again."
      topic: ""
      data: "{action_type:1}"
    parentUserControl:
      appId: 20023
      title: "sea study"
      body: ""
      topic: ""
      data: "{\"change_key\":\"%s\",\"type\":\"2\"}"
    parentSnControl:
      appId: 20023
      title: "sea study"
      body: ""
      topic: ""
      data: "{\"type\":\"1\"}"
    parentDeviceList:
      appId: 20005
      title: "query device list"
      body: "{\"type\":3}"
      topic: ""
      data: "{\"type\":3}"
    childDeviceDelete:
      appId: 20001
      title: "Account deletion pending"
      body: "Contact parents to restore account"
      topic: ""
      data: "{action_type:1,scheme:seausercenter://com.tal.sea.user/login?pkgName=notification&sdkVer=0}"
    childLogoutNotice:
      appId: 20001
      title: "Unbind"
      body: "The current device has been unlinked by the parent. Please click to log in again."
      topic: ""
      data: "{action_type:1,scheme:seausercenter://com.tal.sea.user/login?pkgName=notification&sdkVer=0}"
  bindWaitingTime: 3000
  defaultAvatar: https://static.thinkbuddy.com/pubpad/user/parent_avatar_url.png
  deviceList:
    - deviceType: 0
      deviceName: TalPad T100
      deviceAvatar: https://static.thinkbuddycdn.com/pubpad/family/devicepicture.png
    - deviceType: 1
      deviceName: PawPal
      deviceAvatar: https://static.thinkbuddycdn.com/pubpad/family/devicepicture.png
  boyAvatarList:
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_1.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_2.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_3.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_4.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_5.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/boy_6.png
  girlAvatarList:
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_1.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_2.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_3.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_4.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_5.png
    - https://static.thinkbuddycdn.com/pubpad/user/avatar/girl_6.png
  accountDeleteBeforeItem:
    - Cannot log in or use Think Academy Connect service and account.
    - Lose access to linked devices and unable to control them.
  accountDeleteClockItem: Deleting the account will erase all pet data, including levels, backgrounds, and items.
  pushMsgRetryMaxCount: 3
  paiSouMinOsVersion: V250309
  paiSouControlSuiteKey: control_snap_duration

cube:
  url: http://10.224.130.17:8080/cube-701-devicebase/innerapi/v1/iot/direct-method