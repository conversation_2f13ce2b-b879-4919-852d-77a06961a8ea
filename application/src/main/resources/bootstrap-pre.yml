spring:
  application:
    name: so-619-parent-server
  cloud:
    nacos:
      config:
        server-addr: 10.220.18.10:8848
        namespace: df4400c3-6b59-421c-8c91-26f3243b2592
        extension-configs:
          - data-id: ${spring.application.name}.yml
            group: DEFAULT_GROUP
            refresh: true
        username: pre
        password: nB067uY48Sri4LDQ
      discovery:
        namespace: df4400c3-6b59-421c-8c91-26f3243b2592
        server-addr: 10.220.18.10:8848
        username: pre
        password: nB067uY48Sri4LDQ

