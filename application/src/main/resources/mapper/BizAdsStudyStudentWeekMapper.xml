<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tal.sea.seaover.application.mapper.BizAdsStudyStudentWeekMapper">

    <resultMap type="com.tal.sea.seaover.application.domain.BizAdsStudyStudentWeek" id="BizAdsStudyStudentWeekResult">
        <result property="talId" column="tal_id"/>
        <result property="model" column="model"/>
        <result property="weekDate" column="week_date"/>
        <result property="questionCnt" column="question_cnt"/>
        <result property="knowledgeCnt" column="knowledge_cnt"/>
        <result property="taskCnt" column="task_cnt"/>
        <result property="totalDuration" column="total_duration"/>
        <result property="systemDuration" column="system_duration"/>
        <result property="accurateDuration" column="accurate_duration"/>
        <result property="readingDuration" column="reading_duration"/>
        <result property="toolDuration" column="tool_duration"/>
        <result property="jzxQuestionCorrectRate" column="jzx_question_correct_rate"/>
        <result property="gameLearnDuration" column="game_learn_duration"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="selectBizAdsStudyStudentWeekVo">
        select tal_id,
               model,
               week_date,
               question_cnt,
               knowledge_cnt,
               task_cnt,
               total_duration,
               system_duration,
               accurate_duration,
               reading_duration,
               jzx_question_correct_rate,
               game_learn_duration,
               tool_duration,
               created_at,
               updated_at
        from ads_study_student_week
    </sql>

    <select id="selectBizAdsStudyStudentWeekInfo" parameterType="com.tal.sea.seaover.application.domain.BizAdsStudyStudentWeek"
            resultMap="BizAdsStudyStudentWeekResult">
        <include refid="selectBizAdsStudyStudentWeekVo"/>
        where
        tal_id = #{talId}
        and model = #{model}
        and week_date = #{weekDate}
        limit 1
    </select>

</mapper>