spring:
  application:
    name: so-619-parent-server
  cloud:
    nacos:
      config:
        server-addr: 10.220.18.10:8848
        extension-configs:
          - data-id: ${spring.application.name}.yml
            group: DEFAULT_GROUP
            refresh: true
        username: dev-rw
        password: QOaP%u7L%r
      discovery:
        server-addr: 10.220.18.10:8848
        username: dev-rw
        password: QOaP%u7L%r

