<?xml version="1.0" encoding="UTF-8" ?>
<!--<Configuration xmlns="http://logging.apache.org/log4j/2.0/config">-->
<Configuration>
    <Properties>
        <Property name="LOG_HOME">/home/<USER>/xeslog</Property>
        <Property name="JSON_PATTERN">{"x_timestamp":"%d{yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}","x_level":"%-5level","x_trace_id":"%X{traceId}","x_rpc_id":"%X{rpcId}","x_sn":"%X{sn}","x_tal_id":"%X{talId}","x_duration":"%X{cost}","x_module":"%c{1}.%M():@%L","x_server_ip":"${env:HOSTNAME}","x_msg":"%enc{%m}{JSON}","x_backtrace":"%enc{%ex{short}}{JSON}"}%n</Property>
        <Property name="CONSOLE_PATTERN">%highlight{[%d{yyyy/MM/dd HH:mm:ss.SSS}][%-5level][%X{traceId}:%X{spanId}][%c{1}.%M():@%L] - %msg%n}{FATAL=Bright Red, ERROR=Bright Magenta, WARN=Bright Yellow, INFO=Bright Green, DEBUG=Bright Cyan, TRACE=Bright White}
        </Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${JSON_PATTERN}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>