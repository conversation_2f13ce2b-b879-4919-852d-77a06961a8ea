spring:
  application:
    name: so-619-parent-server
  cloud:
    nacos:
      config:
        server-addr: 10.224.2.7:8848
        extension-configs:
          - data-id: ${spring.application.name}.yml
            group: DEFAULT_GROUP
            refresh: true
        username: nacos
        password: J3dEGf5SCT
      discovery:
        server-addr: 10.224.2.7:8848
        username: nacos
        password: J3dEGf5SCT

